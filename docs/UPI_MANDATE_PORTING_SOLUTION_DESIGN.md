# UPI AutoPay Mandate Interoperability and Porting - Solution Design Document

## 1. Executive Summary

### 1.1 Business Problem
With the introduction of UPI AutoPay mandate interoperability by NPCI, users can now port their existing mandates from one UPI app to another without canceling and recreating them. This enhances user experience and reduces friction in switching between UPI applications.

### 1.2 Solution Overview
This document outlines the technical solution for implementing UPI AutoPay mandate interoperability and porting functionality in the Payment Transaction History (PTH) system. The solution enables seamless mandate execution across different UPI apps and provides comprehensive tracking of mandate porting activities.

### 1.3 Key Benefits
- **Enhanced User Experience**: Users can port mandates without disruption
- **Regulatory Compliance**: Meets NPCI interoperability requirements
- **Operational Efficiency**: Reduces support overhead for mandate recreation
- **Business Growth**: Facilitates user acquisition from other UPI apps

## 2. Business Requirements

### 2.1 Functional Requirements
- **FR-001**: Support mandate execution with purpose code `AZ` (interoperable mandates)
- **FR-002**: Handle mandate porting operations with purpose code `BC`
- **FR-003**: Track previous payer app information for ported mandates
- **FR-004**: Display porting information in transaction history and narration
- **FR-005**: Validate porting transactions for completeness
- **FR-006**: Support both incoming and outgoing port operations

### 2.2 Non-Functional Requirements
- **NFR-001**: Backward compatibility with existing mandate functionality
- **NFR-002**: Performance impact < 5% on existing mandate processing
- **NFR-003**: 99.9% availability for porting operations
- **NFR-004**: Comprehensive audit trail for all porting activities
- **NFR-005**: Support for 10,000+ porting transactions per day

### 2.3 Regulatory Requirements
- **RR-001**: Compliance with NPCI UPI AutoPay interoperability guidelines
- **RR-002**: Support for standardized UMN format across UPI apps
- **RR-003**: Proper handling of mandate sequence numbers for ported mandates
- **RR-004**: Audit trail for regulatory reporting

## 3. Technical Architecture

### 3.1 High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UPI Switch    │    │   Other UPI     │    │   Paytm UPI     │
│     (NPCI)      │◄──►│     Apps        │◄──►│     App         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Event Ingestion Layer                        │
├─────────────────────────────────────────────────────────────────┤
│                   Adaptor Controller                            │
│              • Event Reception                                  │
│              • Initial Validation                               │
│              • Event Routing                                    │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Processing Pipelines                         │
├─────────────────────────┬───────────────────────────────────────┤
│      UPI Pipeline       │         Mandate Pipeline             │
├─────────────────────────┼───────────────────────────────────────┤
│ • UPI Transaction       │ • Mandate Processing                  │
│   Processing            │ • Porting Logic                       │
│ • Validation            │ • Interoperability                    │
│ • Enrichment            │ • Metadata Enrichment                 │
└─────────────────────────┴───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                    PTH System Components                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ Flink Ingestion │   PTH Commons   │      PTH Web API            │
│    Service      │    Library      │       Service               │
├─────────────────┼─────────────────┼─────────────────────────────┤
│ • Data          │ • Constants     │ • API Endpoints             │
│   Enrichment    │ • Utilities     │ • Narration Logic           │
│ • Validation    │ • Data Models   │ • Response Formatting       │
│ • Processing    │ • Enums         │ • User Interface            │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### 3.2 Component Architecture

#### 3.2.1 Event-Driven Data Flow Architecture
```
UPI Switch/Apps → Adaptor Controller → Pipeline Router → Processing → PTH Components
       │                │                    │              │            │
       ▼                ▼                    ▼              ▼            ▼
   Raw Events      Event Validation    UPI/Mandate      Data         API Response
   (JSON/XML)      & Transformation    Pipeline         Enrichment   & Storage
                                      Selection         & Storage
```

#### 3.2.2 Pipeline Processing Flow
```
Adaptor Controller
       │
       ▼
Event Classification
       │
       ├─── UPI Pipeline ────────┐
       │    • Regular UPI Txns   │
       │    • P2P Transfers      │
       │    • Bill Payments      │
       │                         │
       └─── Mandate Pipeline ────┤
            • Purpose AZ (Interop)│
            • Purpose BC (Porting)│
            • Purpose COLLECT     │
                                  │
                                  ▼
                            PTH Processing
                            • Flink Ingestion
                            • Data Enrichment
                            • Storage
                            • API Response
```

#### 3.2.2 Service Layer Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
├─────────────────┬─────────────────┬─────────────────────────┤
│ Mandate         │ Porting         │ Validation              │
│ Processing      │ Service         │ Service                 │
│ Service         │                 │                         │
├─────────────────┼─────────────────┼─────────────────────────┤
│ • Execute       │ • Port          │ • Validate Purpose      │
│   Mandates      │   Validation    │   Codes                 │
│ • Update        │ • Metadata      │ • Check UMN Format      │
│   Status        │   Extraction    │ • Verify Completeness   │
│ • Generate      │ • App Mapping   │ • Business Rules        │
│   Narration     │ • Tracking      │   Validation            │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 4. Solution Design

### 4.1 Data Model Design

#### 4.1.1 Enhanced ActionMetadata
```java
public class ActionMetadata {
    // Existing fields
    Integer executionNo;
    Integer retryAttempt;
    Long pauseEndDate;
    Long expiryDate;
    Long validityEndDate;
    Boolean isBackFilled;
    
    // New field for porting
    Map<String, String> metaDataMap; // Stores porting-specific data
}
```

#### 4.1.2 Context Map Extensions
```java
// New context map keys for porting
public static final String OTHER_UMN = "otherUMN";
public static final String PREVIOUS_PAYER_APP = "previousPayerApp";
public static final String PORT_TYPE = "portType";
public static final String MANDATE_PORT_SEQUENCE = "mandatePortSequence";
```

### 4.2 Business Logic Design

#### 4.2.1 Purpose Code Handling
```
Purpose Code AZ (Interoperable Mandate):
├── Execution Number = 1: Regular mandate creation
├── Execution Number > 1: Ported mandate execution
└── Special handling for narration and tracking

Purpose Code BC (Porting Operation):
├── IN_PORT: Mandate being ported TO Paytm
├── OUT_PORT: Mandate being ported FROM Paytm
└── Metadata tracking for audit trail
```

#### 4.2.2 Validation Logic Flow
```
Input Transaction
    │
    ▼
Is Purpose = AZ or BC?
    │
    ├─ No → Standard Processing
    │
    ▼ Yes
Validate Porting Fields
    │
    ├─ Invalid → Reject Transaction
    │
    ▼ Valid
Extract Porting Metadata
    │
    ▼
Enrich Transaction Data
    │
    ▼
Process & Store
```

### 4.3 API Design

#### 4.3.1 Enhanced Transaction Response
```json
{
  "txnId": "TXN123456789",
  "amount": 500.00,
  "status": "SUCCESS",
  "narration": "Automatic payment of ₹500 (ported from PhonePe) for Netflix",
  "mandateInfo": {
    "umn": "12345@paytm",
    "isPorted": true,
    "previousPayerApp": "PhonePe",
    "portingDate": "2024-01-15T10:30:00Z"
  }
}
```

#### 4.3.2 Porting Status API
```json
{
  "mandateId": "MANDATE123",
  "portingStatus": "COMPLETED",
  "sourceApp": "PhonePe",
  "targetApp": "Paytm",
  "portingDate": "2024-01-15T10:30:00Z",
  "previousUMN": "67890@ybl",
  "newUMN": "12345@paytm"
}
```

## 5. Implementation Strategy

### 5.1 Development Phases

#### Phase 1: Core Infrastructure (Week 1-2)
- Implement new constants and enums
- Enhance data models
- Create MandatePortingUtility class
- Unit testing

#### Phase 2: Processing Logic (Week 3-4)
- Implement porting validation logic
- Enhance mandate processing in Flink service
- Update data enrichment logic
- Integration testing

#### Phase 3: API Enhancement (Week 5-6)
- Update web API responses
- Implement enhanced narration
- Create porting status endpoints
- API testing

#### Phase 4: Testing & Deployment (Week 7-8)
- End-to-end testing
- Performance testing
- Security testing
- Production deployment

### 5.2 Risk Mitigation

#### 5.2.1 Technical Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Performance degradation | High | Low | Comprehensive performance testing |
| Data corruption | High | Low | Extensive validation and rollback procedures |
| Integration issues | Medium | Medium | Thorough integration testing |
| Backward compatibility | Medium | Low | Comprehensive regression testing |

#### 5.2.2 Business Risks
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Regulatory non-compliance | High | Low | Regular compliance reviews |
| User experience issues | Medium | Medium | User acceptance testing |
| Operational overhead | Low | Medium | Automated monitoring and alerting |

### 5.3 Testing Strategy

#### 5.3.1 Unit Testing
- Test coverage > 90% for new components
- Mock external dependencies
- Test edge cases and error scenarios

#### 5.3.2 Integration Testing
- Test mandate porting end-to-end flow
- Validate data consistency across services
- Test API contract compliance

#### 5.3.3 Performance Testing
- Load testing with 10,000+ transactions
- Stress testing for peak scenarios
- Memory and CPU usage monitoring

## 6. Monitoring & Observability

### 6.1 Key Metrics
- **Business Metrics**:
  - Number of ported mandates per day
  - Porting success rate
  - Average porting time
  - User adoption rate

- **Technical Metrics**:
  - API response times
  - Error rates
  - System throughput
  - Resource utilization

### 6.2 Alerting Strategy
- **Critical Alerts**: Porting failure rate > 5%
- **Warning Alerts**: Response time > 2 seconds
- **Info Alerts**: Daily porting volume reports

### 6.3 Logging Strategy
```
Log Level: INFO
- Successful porting operations
- Mandate execution with purpose AZ

Log Level: WARN
- Validation failures
- Incomplete porting data

Log Level: ERROR
- Processing failures
- System errors
```

## 7. Security Considerations

### 7.1 Data Protection
- Encrypt sensitive mandate information
- Implement data masking for logs
- Secure transmission of porting data

### 7.2 Access Control
- Role-based access for porting operations
- Audit trail for all administrative actions
- Secure API endpoints with authentication

### 7.3 Compliance
- GDPR compliance for user data
- PCI DSS compliance for payment data
- RBI guidelines for digital payments

## 8. Deployment Strategy

### 8.1 Environment Strategy
```
Development → Testing → Staging → Production
     │           │         │          │
     ▼           ▼         ▼          ▼
  Unit Tests  Integration  UAT     Blue-Green
             Tests                 Deployment
```

### 8.2 Rollback Plan
- Database migration rollback scripts
- Feature flags for quick disable
- Monitoring for immediate issue detection
- Automated rollback triggers

### 8.3 Go-Live Checklist
- [ ] All tests passed
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Monitoring configured
- [ ] Documentation updated
- [ ] Team training completed

## 9. Success Criteria

### 9.1 Technical Success Criteria
- Zero critical bugs in production
- API response time < 500ms (95th percentile)
- System availability > 99.9%
- Successful processing of 10,000+ porting transactions

### 9.2 Business Success Criteria
- 95% porting success rate
- Positive user feedback score > 4.5/5
- Regulatory compliance certification
- 20% increase in mandate adoption

## 10. Database Design

### 10.1 Schema Changes

#### 10.1.1 Enhanced Mandate Activity Table
```sql
ALTER TABLE mandate_activity_data
ADD COLUMN porting_metadata JSONB,
ADD COLUMN previous_payer_app VARCHAR(50),
ADD COLUMN other_umn VARCHAR(100),
ADD COLUMN port_type VARCHAR(20);

CREATE INDEX idx_mandate_activity_port_type ON mandate_activity_data(port_type);
CREATE INDEX idx_mandate_activity_prev_app ON mandate_activity_data(previous_payer_app);
```

#### 10.1.2 New Porting Audit Table
```sql
CREATE TABLE mandate_porting_audit (
    id BIGSERIAL PRIMARY KEY,
    mandate_id VARCHAR(100) NOT NULL,
    source_umn VARCHAR(100),
    target_umn VARCHAR(100),
    source_app VARCHAR(50),
    target_app VARCHAR(50),
    port_type VARCHAR(20),
    status VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

CREATE INDEX idx_porting_audit_mandate_id ON mandate_porting_audit(mandate_id);
CREATE INDEX idx_porting_audit_status ON mandate_porting_audit(status);
CREATE INDEX idx_porting_audit_date ON mandate_porting_audit(created_date);
```

### 10.2 Data Migration Strategy
```sql
-- Migration script for existing mandates
UPDATE mandate_activity_data
SET porting_metadata = '{}'::jsonb
WHERE porting_metadata IS NULL;

-- Backfill previous payer app from UMN
UPDATE mandate_activity_data
SET previous_payer_app = SPLIT_PART(context_map->>'otherUMN', '@', 2)
WHERE context_map->>'otherUMN' IS NOT NULL
AND previous_payer_app IS NULL;
```

## 11. Configuration Management

### 11.1 Application Properties
```yaml
# Mandate Porting Configuration
mandate:
  porting:
    enabled: true
    validation:
      strict-mode: true
      timeout-seconds: 30
    supported-apps:
      - paytm
      - ybl
      - pthdfc
      - ptyes
      - okaxis
      - okicici
      - oksbi
    retry:
      max-attempts: 3
      backoff-delay: 1000
```

### 11.2 Feature Flags
```yaml
features:
  mandate-interoperability: true
  mandate-porting: true
  enhanced-narration: true
  porting-analytics: false
  bulk-porting: false
```

## 12. Error Handling & Recovery

### 12.1 Error Classification
```java
public enum PortingErrorType {
    INVALID_PURPOSE_CODE("PTG001", "Invalid purpose code for porting"),
    MISSING_OTHER_UMN("PTG002", "Other UMN is required for porting"),
    INVALID_UMN_FORMAT("PTG003", "Invalid UMN format"),
    UNSUPPORTED_PAYER_APP("PTG004", "Payer app not supported"),
    DUPLICATE_PORTING_REQUEST("PTG005", "Duplicate porting request"),
    MANDATE_NOT_FOUND("PTG006", "Source mandate not found"),
    PORTING_TIMEOUT("PTG007", "Porting operation timed out");
}
```

### 12.2 Recovery Mechanisms
```java
@Retryable(value = {PortingException.class}, maxAttempts = 3)
public void processPortingTransaction(TransactionHistoryDetails thd) {
    try {
        validatePortingTransaction(thd);
        enrichPortingMetadata(thd);
        processMandate(thd);
    } catch (PortingException e) {
        logPortingError(e, thd);
        throw e;
    }
}
```

## 13. Performance Optimization

### 13.1 Caching Strategy
```java
@Cacheable(value = "payerAppMapping", key = "#appId")
public String getPayerAppDisplayName(String appId) {
    return payerAppMappings.get(appId);
}

@Cacheable(value = "mandatePortingStatus", key = "#mandateId")
public PortingStatus getPortingStatus(String mandateId) {
    return portingRepository.findByMandateId(mandateId);
}
```

### 13.2 Database Optimization
```sql
-- Partitioning strategy for large tables
CREATE TABLE mandate_activity_data_2024 PARTITION OF mandate_activity_data
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Optimized indexes for porting queries
CREATE INDEX CONCURRENTLY idx_mandate_activity_purpose_date
ON mandate_activity_data(purpose, created_date)
WHERE purpose IN ('AZ', 'BC');
```

## 14. Future Enhancements

### 14.1 Short-term (3-6 months)
- **Bulk Mandate Porting**: Support for porting multiple mandates in a single operation
- **Enhanced Analytics**: Real-time dashboards for porting metrics and trends
- **Mobile App Integration**: Native mobile app support for porting operations
- **Real-time Notifications**: Push notifications for porting status updates
- **Advanced Validation**: ML-based fraud detection for suspicious porting activities

### 14.2 Long-term (6-12 months)
- **AI-Powered Insights**: Predictive analytics for mandate porting patterns
- **Cross-Platform Management**: Unified mandate management across multiple UPI apps
- **International Support**: Support for international mandate porting standards
- **Blockchain Integration**: Immutable audit trail using blockchain technology
- **Voice-Activated Porting**: Voice command support for mandate operations

### 14.3 Innovation Roadmap
```
Q1 2024: Bulk Porting + Enhanced Analytics
Q2 2024: Mobile Integration + Real-time Notifications
Q3 2024: AI-Powered Fraud Detection
Q4 2024: Cross-Platform Management
Q1 2025: International Standards Support
Q2 2025: Blockchain Integration
```

## 15. Appendices

### 15.1 Glossary
- **UMN**: Unique Mandate Number - Identifier for UPI AutoPay mandates
- **Purpose Code**: Transaction purpose identifier (AZ for interoperable, BC for porting)
- **Porting**: Process of transferring mandate from one UPI app to another
- **Interoperability**: Ability to execute mandates across different UPI applications

### 15.2 Reference Documents
- NPCI UPI AutoPay Interoperability Guidelines v2.0
- RBI Guidelines on Digital Payment Security
- PTH System Architecture Document v3.1
- UPI Technical Specifications v2.5

### 15.3 Contact Information
- **Technical Lead**: [Name] - [email]
- **Product Manager**: [Name] - [email]
- **Architecture Team**: [email]
- **DevOps Team**: [email]

---

**Document Version**: 1.0
**Last Updated**: December 2024
**Prepared By**: Development Team
**Reviewed By**: Architecture Team
**Approved By**: Product Management
