# Event-Driven Pipeline Architecture for UPI Mandate Porting

## Overview

This document describes how the UPI AutoPay Mandate Interoperability and Porting functionality is implemented using an event-driven pipeline architecture where events are ingested from the adaptor controller and processed through specialized UPI and mandate pipelines.

## Architecture Flow

### 1. Event Ingestion Layer
```
UPI Switch/NPCI → Adaptor Controller → Event Classification → Pipeline Router
```

### 2. Pipeline Processing Layer
```
Pipeline Router → UPI Pipeline / Mandate Pipeline → PTH Processing
```

### 3. PTH System Integration
```
PTH Processing → Flink Ingestion → Data Enrichment → Storage → Web API
```

## Event Processing Flow

### Step 1: Event Reception at Adaptor Controller
The adaptor controller receives events from various sources:
- **UPI Switch (NPCI)**: Standard UPI transactions and mandate operations
- **Other UPI Apps**: Interoperable mandate executions and porting requests
- **Paytm UPI App**: Internal mandate operations and porting responses

### Step 2: Event Classification
Events are classified based on transaction type:
```java
// In MandatePortingFlowController.processTransaction()
if (!TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
    // Route to UPI Pipeline
    return createStandardFlow("UPI_PIPELINE", "Routed to UPI pipeline");
} else {
    // Route to Mandate Pipeline
    return routeToMandatePipeline(thd);
}
```

### Step 3: Pipeline Routing
Based on the transaction type and purpose code, events are routed to appropriate pipelines:

#### UPI Pipeline
Handles non-mandate transactions:
- P2P transfers
- Bill payments
- Regular UPI transactions
- Merchant payments

#### Mandate Pipeline
Handles mandate-related transactions with sub-pipelines:

##### 3.1 Interoperability Pipeline (Purpose AZ)
```java
private static MandateFlowResult handleInteroperabilityPipeline(TransactionHistoryDetails thd) {
    String executionNo = getExecutionNumber(thd);
    
    if ("1".equals(executionNo)) {
        // Create new interoperable mandate
        return createInteroperableMandate();
    } else {
        // Execute ported mandate
        return executePortedMandate();
    }
}
```

##### 3.2 Porting Pipeline (Purpose BC)
```java
private static MandateFlowResult handlePortingPipeline(TransactionHistoryDetails thd) {
    String portType = getPortType(thd);
    
    if (PURPOSE_IN_PORT.equals(portType)) {
        return handlePortInPipeline(thd);  // Mandate coming TO Paytm
    } else if (PURPOSE_OUT_PORT.equals(portType)) {
        return handlePortOutPipeline(thd); // Mandate going FROM Paytm
    }
}
```

##### 3.3 Standard Mandate Pipeline (Purpose COLLECT)
```java
private static MandateFlowResult handleStandardMandatePipeline(TransactionHistoryDetails thd) {
    return processStandardMandate(thd);
}
```

## Pipeline Processing Details

### Interoperability Pipeline Processing

#### Create Flow (Execution #1)
1. **Event Reception**: Adaptor controller receives mandate creation event
2. **Classification**: Identified as RECURRING_MANDATE with purpose AZ
3. **Pipeline Routing**: Routed to Interoperability Pipeline
4. **Processing**: 
   - Create new mandate with interoperable flag
   - Set up UMN for cross-app execution
   - Store mandate metadata
5. **PTH Integration**: Data flows to Flink ingestion for storage

#### Execution Flow (Execution >1)
1. **Event Reception**: Adaptor controller receives execution event
2. **Classification**: RECURRING_MANDATE with purpose AZ, execution >1
3. **Pipeline Routing**: Routed to Interoperability Pipeline
4. **Processing**:
   - Validate porting metadata (otherUMN, previousPayerApp)
   - Enrich transaction with porting information
   - Execute mandate with enhanced narration
5. **PTH Integration**: Enhanced data flows to PTH system

### Porting Pipeline Processing

#### Incoming Port Flow (IN_PORT)
1. **Event Reception**: Mandate being ported TO Paytm
2. **Classification**: RECURRING_MANDATE with purpose BC, portType IN_PORT
3. **Pipeline Routing**: Routed to Porting Pipeline → Port IN sub-pipeline
4. **Processing**:
   - Validate porting transaction completeness
   - Extract source app information from otherUMN
   - Create porting audit record
   - Update mandate with porting metadata
5. **PTH Integration**: Porting event processed and stored

#### Outgoing Port Flow (OUT_PORT)
1. **Event Reception**: Mandate being ported FROM Paytm
2. **Classification**: RECURRING_MANDATE with purpose BC, portType OUT_PORT
3. **Pipeline Routing**: Routed to Porting Pipeline → Port OUT sub-pipeline
4. **Processing**:
   - Validate outgoing port request
   - Update mandate status
   - Create porting audit record
   - Notify target app
5. **PTH Integration**: Outgoing port event processed

## Implementation Components

### 1. MandatePortingFlowController
**Purpose**: Main pipeline router and event processor
**Key Methods**:
- `processTransaction()`: Main entry point for event processing
- `handleInteroperabilityPipeline()`: Processes AZ purpose events
- `handlePortingPipeline()`: Processes BC purpose events
- `handleStandardMandatePipeline()`: Processes COLLECT purpose events

### 2. MandatePortingUtility
**Purpose**: Utility functions for porting operations
**Key Methods**:
- `isInteroperableMandate()`: Identifies AZ purpose mandates
- `isMandatePortingTransaction()`: Identifies BC purpose transactions
- `isValidPortingTransaction()`: Validates porting event completeness
- `extractPayerAppFromUMN()`: Extracts app info from UMN

### 3. Enhanced Data Models
**ActionMetadata**: Added `metaDataMap` for porting information
**Context Map Keys**: Added porting-specific keys (otherUMN, previousPayerApp, etc.)
**Mandate Actions**: Added IN_PORT and OUT_PORT actions

## Event Flow Examples

### Example 1: Interoperable Mandate Creation
```json
{
  "txnType": "RECURRING_MANDATE",
  "purpose": "AZ",
  "executionNo": "1",
  "amount": 500,
  "merchantName": "Netflix"
}
```
**Pipeline Flow**: Adaptor Controller → Mandate Pipeline → Interoperability Pipeline → Create Flow → PTH

### Example 2: Ported Mandate Execution
```json
{
  "txnType": "RECURRING_MANDATE",
  "purpose": "AZ",
  "executionNo": "3",
  "otherUMN": "12345@phonepe",
  "previousPayerApp": "phonepe",
  "amount": 500
}
```
**Pipeline Flow**: Adaptor Controller → Mandate Pipeline → Interoperability Pipeline → Execution Flow → PTH

### Example 3: Incoming Port Operation
```json
{
  "txnType": "RECURRING_MANDATE",
  "purpose": "BC",
  "portType": "IN_PORT",
  "otherUMN": "67890@ybl",
  "previousPayerApp": "ybl",
  "mandateId": "MANDATE123"
}
```
**Pipeline Flow**: Adaptor Controller → Mandate Pipeline → Porting Pipeline → Port IN Flow → PTH

## Benefits of Pipeline Architecture

### 1. Scalability
- **Parallel Processing**: Different pipelines can process events concurrently
- **Load Distribution**: Events distributed based on type and purpose
- **Resource Optimization**: Specialized processing for different event types

### 2. Maintainability
- **Separation of Concerns**: Each pipeline handles specific functionality
- **Modular Design**: Easy to modify individual pipelines
- **Clear Boundaries**: Well-defined interfaces between components

### 3. Reliability
- **Error Isolation**: Failures in one pipeline don't affect others
- **Retry Mechanisms**: Pipeline-specific retry strategies
- **Monitoring**: Individual pipeline health monitoring

### 4. Extensibility
- **New Pipelines**: Easy to add new processing pipelines
- **Feature Flags**: Pipeline-level feature toggles
- **A/B Testing**: Route events to different pipeline versions

## Monitoring and Observability

### Pipeline Metrics
- **Event Volume**: Number of events processed per pipeline
- **Processing Time**: Average processing time per pipeline
- **Error Rates**: Error rates for each pipeline
- **Success Rates**: Success rates for porting operations

### Logging Strategy
```java
// Pipeline entry logging
log.info("Event received in {} pipeline: {}", pipelineType, eventId);

// Pipeline processing logging
log.info("Processing {} event in {} pipeline", eventType, pipelineType);

// Pipeline completion logging
log.info("Event {} processed successfully in {} pipeline", eventId, pipelineType);
```

### Alerting
- **Pipeline Failures**: Alert when pipeline error rate exceeds threshold
- **Processing Delays**: Alert when processing time exceeds SLA
- **Volume Anomalies**: Alert on unusual event volume patterns

## Future Enhancements

### 1. Advanced Pipeline Features
- **Dynamic Routing**: Route events based on real-time conditions
- **Pipeline Chaining**: Chain multiple pipelines for complex processing
- **Event Replay**: Replay events through pipelines for testing

### 2. Performance Optimizations
- **Batch Processing**: Process multiple events in batches
- **Caching**: Cache frequently accessed data in pipelines
- **Async Processing**: Asynchronous event processing

### 3. Enhanced Monitoring
- **Real-time Dashboards**: Live pipeline performance dashboards
- **Predictive Analytics**: Predict pipeline performance issues
- **Automated Scaling**: Auto-scale pipelines based on load

---

This event-driven pipeline architecture provides a robust, scalable, and maintainable foundation for processing UPI mandate porting events while ensuring high performance and reliability.
