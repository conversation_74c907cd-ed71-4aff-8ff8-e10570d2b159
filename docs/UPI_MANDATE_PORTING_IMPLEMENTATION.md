# UPI AutoPay Mandate Interoperability and Porting Implementation

## Overview

This document describes the implementation of UPI AutoPay mandate interoperability and porting functionality in the Payment Transaction History (PTH) system. The implementation allows users to port their existing mandates from other UPI apps to Paytm and vice versa, enabling seamless mandate management across different UPI applications.

## Key Features

### 1. Mandate Interoperability
- Support for purpose code `AZ` for interoperable mandates
- Ability to execute mandates created on other UPI apps
- Seamless handling of mandates without requiring CREATE transactions

### 2. Mandate Porting
- Support for purpose code `BC` for porting operations
- `IN_PORT` transactions for mandates being ported to Paytm
- `OUT_PORT` transactions for mandates being ported from Paytm
- Tracking of previous payer app information

### 3. Enhanced User Experience
- Updated narration to show porting information
- Display of previous payer app in transaction history
- Proper handling of mandate sequence numbers for ported mandates

## Technical Implementation

### 1. New Constants Added

#### Purpose Codes
```java
// CommonConstants.java
public static final String PURPOSE_AZ = "AZ";           // Interoperable mandates
public static final String PURPOSE_BC = "BC";           // Porting operations
public static final String PURPOSE_IN_PORT = "IN_PORT"; // Incoming port
public static final String PURPOSE_OUT_PORT = "OUT_PORT"; // Outgoing port
```

#### Context Map Keys
```java
// UthDtoContextMapKeyConstants.java
public static final String OTHER_UMN = "otherUMN";                    // Previous UMN
public static final String PREVIOUS_PAYER_APP = "previousPayerApp";   // Previous app name
public static final String PORT_TYPE = "portType";                    // Port operation type
public static final String MANDATE_PORT_SEQUENCE = "mandatePortSequence"; // Port sequence
```

### 2. New Mandate Actions

#### MandateActionEnum
```java
IN_PORT(8),   // Incoming port operation
OUT_PORT(9)   // Outgoing port operation
```

### 3. Core Utility Class

#### MandatePortingUtility
A comprehensive utility class providing methods for:
- Identifying interoperable mandates
- Detecting porting transactions
- Validating porting operations
- Extracting payer app information from UMN
- Generating display names for narration

Key methods:
- `isInteroperableMandate()` - Checks for purpose AZ mandates
- `isMandatePortingTransaction()` - Checks for purpose BC transactions
- `isPortedMandate()` - Identifies ported mandates (no CREATE transaction)
- `extractPayerAppFromUMN()` - Extracts payer app from UMN format
- `getPayerAppDisplayName()` - Maps app identifiers to display names

### 4. Enhanced Data Models

#### ActionMetadata
Added `metaDataMap` field to store porting-specific information:
```java
@JsonProperty("metaDataMap")
Map<String, String> metaDataMap;
```

This field stores:
- `otherUMN`: Previous UMN from the source app
- `previousPayerApp`: Source payer app identifier
- `portType`: Type of porting operation (IN_PORT/OUT_PORT)

### 5. Processing Logic Updates

#### Mandate Processing (MandateUtility)
- Enhanced to handle porting metadata
- Extraction and storage of previous payer app information
- Support for both AZ and BC purpose codes

#### Data Enrichment (MandateDataEnricher)
- New method `updateParentDocForRecurringMandatePortEvent()`
- Proper handling of IN_PORT and OUT_PORT actions
- Metadata propagation from activity to parent document

#### Narration Updates (Web MandateUtility)
- Enhanced narration for ported mandates
- Display of previous payer app information
- Special handling for mandates with purpose AZ

## Transaction Flow Examples

### 1. Interoperable Mandate Execution
```
Purpose: AZ
Transaction Type: RECURRING_MANDATE
Execution Number: 2+ (no CREATE transaction)
Context: {
  "executionNo": "2",
  "otherUMN": "12345@previousapp",
  "previousPayerApp": "previousapp"
}
```

### 2. Incoming Port Transaction
```
Purpose: BC
Transaction Type: RECURRING_MANDATE
Context: {
  "portType": "IN_PORT",
  "otherUMN": "12345@sourceapp",
  "previousPayerApp": "sourceapp"
}
```

### 3. Outgoing Port Transaction
```
Purpose: BC
Transaction Type: RECURRING_MANDATE
Context: {
  "portType": "OUT_PORT",
  "otherUMN": "67890@targetapp"
}
```

## Narration Examples

### Before Porting
```
"Automatic payment of ₹500 for Netflix subscription"
```

### After Porting (Interoperable Mandate)
```
"Automatic payment of ₹500 (ported from PhonePe) for Netflix subscription"
```

## Validation Rules

### 1. Interoperable Mandate Validation
- Must have purpose code `AZ`
- Must be `RECURRING_MANDATE` transaction type
- Execution number should be > 1 for ported mandates

### 2. Porting Transaction Validation
- Must have purpose code `BC`
- Must include `portType` (IN_PORT or OUT_PORT)
- Must include `otherUMN` for tracking previous mandate
- Must be `RECURRING_MANDATE` transaction type

### 3. UMN Format Validation
- UMN format: `{mandateId}@{payerApp}`
- Example: `12345@paytm`, `67890@ybl`

## Supported Payer Apps

The system includes display name mappings for common UPI apps:
- `paytm` → "Paytm"
- `ybl` → "PhonePe"
- `pthdfc` → "HDFC Bank"
- `ptyes` → "Yes Bank"
- `okaxis` → "Axis Bank"
- `okicici` → "ICICI Bank"
- `oksbi` → "SBI"

## Testing

### Unit Tests
- `MandatePortingUtilityTest.java` - Comprehensive test coverage for all utility methods
- Tests for validation, extraction, and identification logic
- Edge case handling for invalid inputs

### Test Scenarios
1. Interoperable mandate identification
2. Porting transaction validation
3. UMN parsing and payer app extraction
4. Narration generation for ported mandates
5. Metadata handling and propagation

## Configuration

No additional configuration is required. The implementation uses existing infrastructure with new constants and enhanced processing logic.

## Backward Compatibility

The implementation is fully backward compatible:
- Existing mandates continue to work without changes
- New purpose codes are additive
- Enhanced narration only applies to new ported mandates
- Existing validation logic remains intact

## Future Enhancements

1. **Enhanced Analytics**: Track porting success rates and user adoption
2. **Bulk Porting**: Support for porting multiple mandates simultaneously
3. **Cross-App Notifications**: Notify users about successful porting operations
4. **Advanced Validation**: Enhanced UMN validation and duplicate detection
5. **Reporting**: Detailed reports on mandate porting activities

## Monitoring and Logging

The implementation includes comprehensive logging for:
- Porting transaction processing
- Validation failures
- Metadata extraction and storage
- Narration generation

Log levels are appropriately set to aid in debugging and monitoring without overwhelming the system.
