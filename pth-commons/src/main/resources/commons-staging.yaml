async:
  corePool: 5
  maxPool: 7
  queueCapacity: 10

kafkaAsync:
  corePool: 2
  maxPool: 5
  queueCapacity: 200

rewindAsync:
  corePool: 6
  maxPool: 15
  queueCapacity: 25

kafkaTargetObject:
  topic: middleware_transaction_history_uth_localisation_data_mwstaging
  batchSizeBytes: 30000
  confluentKafkaRegistryUrl: http://schema-registry.uth.paytm.local:8081/
  lingerMs: 15
  requestTimeoutMs: 5000
  bootstrapServers:
    - stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
    - stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092
    - stage-kafka1.uth.paytm.local:9092,stage-kafka2.uth.paytm.local:9092,stage-kafka3.uth.paytm.local:9092

searchFieldsNormalizer:
  searchOtherName:
    ingester: lowerCase
    web: camelCase
  searchSelfBankName:
    ingester: lowerCase
    web: camelCase
  searchOtherBankName:
    ingester: lowerCase
    web: camelCase

thirdPartyServicesConfig:
  services:
    -
      thirdPartyServiceName: PMS_SERVICE
      baseUrl: https://pms-ite.orgk.com
      path: /pms/admin/int/v1/product/mapping
      timeouts:
        connectionRequestTimeout: 1000
        connectTimeout: 1000
        socketTimeout: 1000
    -
      thirdPartyServiceName: BMS_SERVICE
      baseUrl: https://bms-ite-internal.orgk.com
      path: /coms/int/v2/contact/beneficiary/details
      timeouts:
        connectionRequestTimeout: 1000
        connectTimeout: 1000
        socketTimeout: 1000
    -
      thirdPartyServiceName: DEFAULT
      timeouts:
        connectionRequestTimeout: 5000
        connectTimeout: 5000
        socketTimeout: 5000