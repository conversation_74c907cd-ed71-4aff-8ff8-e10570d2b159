package com.org.panaroma.commons.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.OTHER_UMN;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.PREVIOUS_PAYER_APP;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.PORT_TYPE;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.MandateActionEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * Utility class for handling UPI AutoPay mandate interoperability and porting
 * functionality. This class provides methods to identify, validate, and process mandate
 * porting operations.
 */
public class MandatePortingUtility {

	/**
	 * Checks if the transaction is an interoperable mandate with purpose code AZ.
	 * @param thd Transaction history details
	 * @return true if it's an interoperable mandate, false otherwise
	 */
	public static boolean isInteroperableMandate(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd) || Objects.isNull(thd.getContextMap())) {
			return false;
		}

		String txnPurpose = com.org.panaroma.commons.utils.Utility.getPurpose(thd);
		return PURPOSE_AZ.equals(txnPurpose) && TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType());
	}

	/**
	 * Checks if the transaction is a mandate porting operation with purpose code BC.
	 * @param thd Transaction history details
	 * @return true if it's a mandate porting operation, false otherwise
	 */
	public static boolean isMandatePortingTransaction(final TransactionHistoryDetails thd) {
		if (Objects.isNull(thd) || Objects.isNull(thd.getContextMap())) {
			return false;
		}

		String txnPurpose = com.org.panaroma.commons.utils.Utility.getPurpose(thd);
		return PURPOSE_BC.equals(txnPurpose) && TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType());
	}

	/**
	 * Checks if the transaction is an incoming port operation.
	 * @param thd Transaction history details
	 * @return true if it's an incoming port, false otherwise
	 */
	public static boolean isIncomingPort(final TransactionHistoryDetails thd) {
		if (!isMandatePortingTransaction(thd)) {
			return false;
		}

		String portType = thd.getContextMap().get(PORT_TYPE);
		return PURPOSE_IN_PORT.equals(portType);
	}

	/**
	 * Checks if the transaction is an outgoing port operation.
	 * @param thd Transaction history details
	 * @return true if it's an outgoing port, false otherwise
	 */
	public static boolean isOutgoingPort(final TransactionHistoryDetails thd) {
		if (!isMandatePortingTransaction(thd)) {
			return false;
		}

		String portType = thd.getContextMap().get(PORT_TYPE);
		return PURPOSE_OUT_PORT.equals(portType);
	}

	/**
	 * Checks if the mandate was ported (doesn't have a CREATE transaction).
	 * @param tthd Transformed transaction history detail
	 * @return true if the mandate was ported, false otherwise
	 */
	public static boolean isPortedMandate(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || Objects.isNull(tthd.getContextMap())) {
			return false;
		}

		// Check if this is an execution with purpose AZ and sequence number > 1
		String txnPurpose = com.org.panaroma.commons.utils.Utility.getPurpose(tthd);
		String executionNo = tthd.getContextMap().get("executionNo");

		return PURPOSE_AZ.equals(txnPurpose)
				&& TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(tthd.getTxnType())
				&& StringUtils.isNotBlank(executionNo) && !executionNo.equals("1");
	}

	/**
	 * Gets the previous payer app information from the context map.
	 * @param contextMap Transaction context map
	 * @return Previous payer app name or null if not available
	 */
	public static String getPreviousPayerApp(final Map<String, String> contextMap) {
		if (Objects.isNull(contextMap)) {
			return null;
		}
		return contextMap.get(PREVIOUS_PAYER_APP);
	}

	/**
	 * Gets the other UMN (previous UMN) from the context map.
	 * @param contextMap Transaction context map
	 * @return Other UMN or null if not available
	 */
	public static String getOtherUMN(final Map<String, String> contextMap) {
		if (Objects.isNull(contextMap)) {
			return null;
		}
		return contextMap.get(OTHER_UMN);
	}

	/**
	 * Extracts the payer app name from UMN.
	 * @param umn Unique Mandate Number
	 * @return Payer app name or null if UMN is invalid
	 */
	public static String extractPayerAppFromUMN(final String umn) {
		if (StringUtils.isBlank(umn) || !umn.contains("@")) {
			return null;
		}

		String[] parts = umn.split("@");
		if (parts.length >= 2) {
			return parts[1]; // Return the part after @
		}
		return null;
	}

	/**
	 * Gets the mandate action for porting transactions.
	 * @param thd Transaction history details
	 * @return MandateActionEnum for porting operations
	 */
	public static MandateActionEnum getPortingMandateAction(final TransactionHistoryDetails thd) {
		if (isIncomingPort(thd)) {
			return MandateActionEnum.IN_PORT;
		}
		else if (isOutgoingPort(thd)) {
			return MandateActionEnum.OUT_PORT;
		}

		// For interoperable mandates with purpose AZ, return COLLECT
		if (isInteroperableMandate(thd)) {
			return MandateActionEnum.COLLECT;
		}

		return null;
	}

	/**
	 * Validates if the mandate porting transaction has all required fields.
	 * @param thd Transaction history details
	 * @return true if valid, false otherwise
	 */
	public static boolean isValidPortingTransaction(final TransactionHistoryDetails thd) {
		if (!isMandatePortingTransaction(thd)) {
			return false;
		}

		Map<String, String> contextMap = thd.getContextMap();
		if (Objects.isNull(contextMap)) {
			return false;
		}

		// Check required fields for porting
		String portType = contextMap.get(PORT_TYPE);
		String otherUMN = contextMap.get(OTHER_UMN);

		return StringUtils.isNotBlank(portType)
				&& (PURPOSE_IN_PORT.equals(portType) || PURPOSE_OUT_PORT.equals(portType))
				&& StringUtils.isNotBlank(otherUMN);
	}

	/**
	 * Checks if the transaction should be processed for mandate interoperability. This
	 * includes both ported mandates and new interoperable mandates.
	 * @param thd Transaction history details
	 * @return true if should be processed for interoperability, false otherwise
	 */
	public static boolean shouldProcessForInteroperability(final TransactionHistoryDetails thd) {
		return isInteroperableMandate(thd) || isMandatePortingTransaction(thd) || isValidPortingTransaction(thd);
	}

	/**
	 * Gets the display name for the previous payer app for narration purposes.
	 * @param payerApp Payer app identifier
	 * @return Display name for the payer app
	 */
	public static String getPayerAppDisplayName(final String payerApp) {
		if (StringUtils.isBlank(payerApp)) {
			return "Previous UPI App";
		}

		// Map common payer app identifiers to display names
		switch (payerApp.toLowerCase()) {
			case "paytm":
				return "Paytm";
			case "pthdfc":
				return "HDFC Bank";
			case "ptyes":
				return "Yes Bank";
			case "ybl":
				return "PhonePe";
			case "okaxis":
				return "Axis Bank";
			case "okicici":
				return "ICICI Bank";
			case "oksbi":
				return "SBI";
			default:
				return payerApp.toUpperCase() + " UPI App";
		}
	}

}
