package com.org.panaroma.commons.constants;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CommonConstants {

	public static final String CHAT_BACK_FILLING_KAFKA_CLIENT_NAME = "chat_back_filling";

	public static final String UPI_BACK_FILLING_KAFKA_CLIENT_NAME = "upi_back_filling";

	public static final String COMMON_BACK_FILLING_SERVICE = "CommonBackFillingService";

	public static final String UPI_BACK_FILLING_PIPELINE = "upi-back-filling";

	public static final String TAG = "tag";

	public static final String UPI = "UPI";

	public static final String FROM_DATE = "fromDate";

	public static final String TO_DATE = "toDate";

	public static final String PAGE_SIZE = "pageSize";

	public static final String PAGE_NO = "pageNo";

	public static final String UPI_BACK_FILLING = UPI_BACK_FILLING_PIPELINE;

	public static final String TRANSACTION_DATE_EPOCH = "transactionDateEpoch";

	public static final String PAGINATION_STREAM_SOURCE = "paginationStreamSource";

	public static final String PAGINATION_TXN_ID = "paginationTxnId";

	public static final String TRUE = "true";

	public static final int DEFAULT_PAGE_SIZE = 200;

	public static final String RECON_CRON_5_TO_30_MINUTES = "reconCron5To30Minutes";

	public static final String RECON_CONFIG_KAFKA_CLIENT_NAME = "recon_config";

	public static final String NON_MERGED_OMS_EVENTS_RECON = "nonMergedOmsEventsRecon";

	public static final String INVISIBLE_MERGED_OMS_EVENTS_RECON = "invisibleMergedOmsEventsRecon";

	public static final String UPI_ONUS_PENDING_EVENTS_RECON = "upiOnusPendingEventsRecon";

	public static final String OMS_REMERGING_RECON = "omsRemergingRecon";

	public static final String OMS_INVISIBLE_MERGED_EVENTS_RECON = "omsInvisibleMergedEventsRecon";

	public static final String STATUS_RESOLVER_KAFKA_CLIENT_NAME = "status-resolver";

	public static final String TOGGLE_VISIBILITY_KAFKA_CLIENT_NAME = "toggle-visibility";

	public static final String TRANSACTION_DATE_EPOCH_TIME = "txnDate";

	public static final String TRANSACTION_ID = "txnId";

	public static final String ENTITY_ID = "entityId";

	public static final String DISTINCT_ENTITY_ID = "distinctEntityId";

	public static final String STREAM_SOURCE = "streamSource";

	public static final String KAFKASINK = "-kafka-sink";

	public static final String SINK = "-sink";

	public static final String INPUT_LCHG_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

	public static final String VALID_LCHG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String REASON_FOR_NOT_SHOWING_IN_LISTING = "reasonForNotInListing";

	public static final String PURPOSE_CREATE = "CREATE";

	public static final String PURPOSE_COLLECT = "COLLECT";

	public static final String PURPOSE_PAY = "PAY";

	public static final String PURPOSE_UPDATE = "UPDATE";

	public static final String PURPOSE_REVOKE = "REVOKE";

	// New purpose codes for mandate interoperability and porting
	public static final String PURPOSE_AZ = "AZ";

	public static final String PURPOSE_BC = "BC";

	public static final String PURPOSE_IN_PORT = "IN_PORT";

	public static final String PURPOSE_OUT_PORT = "OUT_PORT";

	// NTU Cache stands for Non Transacting User Cache
	public static final String TOTAL_CACHE_GET_CALLS = "TOTAL_CACHE_GET_CALLS";

	public static final String TOTAL_CACHE_SAVE_CALLS = "TOTAL_CACHE_SAVE_CALLS";

	public static final String TOTAL_CACHE_DELETE_CALLS = "TOTAL_CACHE_DELETE_CALLS";

	public static final String EXCEPTION_WHILE_GETTING_DATA_FROM_AEROSPIKE_CACHE = "EXCEPTION_WHILE_GETTING_DATA_FROM_AEROSPIKE_CACHE";

	public static final String EXCEPTION_WHILE_PUSHING_NTU_DATA_TO_KAFKA = "EXCEPTION_WHILE_PUSHING_NTU_DATA_TO_KAFKA";

	public static final String SUCCESSFULLY_PUSHED_NTU_DATA_TO_KAFKA = "SUCCESSFULLY_PUSHED_NTU_DATA_TO_KAFKA";

	public static final String CACHE = "CACHE";

	public static final String ISDELETED = "ISDELETED";

	public static final String TIME_WHEN_LISTING_RESPONSE_PUSHED_FOR_CACHEING = "timeWhenListingResponsePushedForCacheing";

	public static final String IS_FOR_GROUPING = "isForGrouping";

	public static final double ONE_DAY_IN_MILLIS = 24.0 * 60 * 60 * 1000;

	public static final String FIR_PURPOSE_CODE = "48";

	public static final String OTM_PURPOSE_CODE = "25";

	public static final String OTM_MCC_CODE = "6211";

	public static final String BACKFILLING_IDENTIFIER = "backFillingIdentifier";

	public static final String IS_BACKFILLING_FOR_PTH_DB = "isBackfillingForPthDb";

	public static final String IS_BACKFILLING_FOR_MANDATE_DB = "isBackfillingForMandateDb";

	// Upi Lite Constants Class
	public class UpiLiteConstants {

		public static final String IS_UPI_LITE_TXN = "isUpiLiteTxn";

		public static final String IS_UPI_LITE_PAYMENT_INSTRUMENT = "isUpiLitePaymentInstrument";

	}

	public class UthAnalyticsKafkaConstants {

		public static final String UTH_ANALYTICS_KAFKA_CLIENT_NAME = "uthAnalyticsKafkaClient";

	}

	// merger constants
	public static final String IS_ADDED_PARTICIPANT_BY_MERGING = "isAddedParticipantByMerging";

	public static final String IGNORED_PARTICIPANT = "ignoredParticipant";

	public static final String ADDNPAY_REFUND_BACK_TO_SOURCE = "ADDNPAY_REFUND_BACK_TO_SOURCE";

	public static final String IS_ADD_N_PAY_REFUND = "isAddNPayRefund";

	public static final List<String> OUTWARD_REPORT_CODES_SENT_TO_CHAT = Arrays.asList("20212", "20420");

	public static final String IS_WALLET_INETROP_TXN = "isWalletInterOpTxn";

	public static final String EMI_PARTIAL_EVENT = "emi_partial_event";

	public static final List<TransactionTypeEnum> WALLET_INTEROP_TXN_TYPE_LIST = Arrays.asList(
			TransactionTypeEnum.UPI_WALLET_CREDIT, TransactionTypeEnum.WALLET_UPI_DEBIT_REVERSAL,
			TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL, TransactionTypeEnum.WALLET_UPI_DEBIT_P2M,
			TransactionTypeEnum.WALLET_UPI_DEBIT_P2P);

	public static final Map<String, String> SEARCH_CONTEXT_TO_ES_MAPPINGS;

	public static final List<Integer> TERMINAL_STATUS_LIST = Arrays.asList(ClientStatusEnum.FAILURE.getStatusKey(),
			ClientStatusEnum.SUCCESS.getStatusKey());

	public static final List<TransactionSource> PG_TYPE_SOURCE_LIST = Arrays.asList(TransactionSource.PG,
			TransactionSource.PPBL_PG);

	public static List<ClientStatusEnum> TERMINAL_STATUS = Arrays.asList(ClientStatusEnum.FAILURE,
			ClientStatusEnum.SUCCESS);

	static {
		Map<String, String> map = new HashMap<>();
		map.put("merchantCategory", "participants.merchantData.merchantCategory");
		map.put("streamSource", "streamSource");
		map.put("txnCategory", "txnType");
		map.put("tag", "tags.tag");
		map.put("entityId", "entityId");
		map.put("secondPartyId", "participants.entityId");
		map.put("secondPartyType", "participants.entityType");
		map.put("walletType", "participants.walletData.walletType");
		map.put("upiVpa", "participants.upiData.vpa.keyword");
		map.put("cardNumber", "participants.cardData.cardNum");
		map.put("cardType", "participants.cardData.cardType");
		map.put("isVisible", "isVisible");
		map.put("showInListing", "showInListing");
		map.put("txnType", "txnType");
		map.put("esDocTxnIndicator", "txnIndicator");
		map.put("esDocStatus", "originalStatus");
		map.put("esDocViewStatus", "status");
		map.put("secondPartyName", "participants.name");
		map.put("showBankData", "isBankData");
		map.put("name", "participants.name");
		map.put("benefName", "participants.contextMap.benefName");
		map.put("remitterName", "participants.contextMap.remitterName");
		map.put("paymentSystem", "participants.paymentSystem");
		map.put("ifsc", "participants.bankData.ifsc.keyword");
		map.put("reportCode", "contextFilterMap.reportCode");
		map.put("txnId", "txnId");
		map.put("rrn", "participants.bankData.rrn");
		map.put("parentTxnId", "parentTxnId");
		map.put("mobileNumber", "participants.mobileData.mobileNumber");
		map.put("sourceTxnId", "sourceTxnId");
		map.put("channelCode", "searchFields.searchChannelCode");
		map.put("participantPaymentSystem", "participants.paymentSystem");
		map.put("backFillingIdentifier", "contextMap.backFillingIdentifier");
		map.put("reasonForNotVisible", "contextMap.reasonForNotVisible");
		map.put("showInHistory", "showInHistory");
		map.put("umn", "umn");
		map.put("txnIds", "txnId");
		map.put("isHideHiddenTxns", "isHiddenTxn");
		map.put("searchTag", "searchFields.searchTags.keyword");
		SEARCH_CONTEXT_TO_ES_MAPPINGS = Collections.unmodifiableMap(map);
	}

	public static final Map<String, String> MANDATE_SEARCH_CONTEXT_TO_ES_MAPPINGS;

	static {
		Map<String, String> map = new HashMap<>();
		map.put("entityId", "entityId");
		map.put("showInHistory", "showInHistory");
		map.put("umn", "umn");

		MANDATE_SEARCH_CONTEXT_TO_ES_MAPPINGS = Collections.unmodifiableMap(map);
	}

	public static final List<TransactionTypeEnum> MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES = Arrays.asList(
			TransactionTypeEnum.RECURRING_MANDATE, TransactionTypeEnum.IPO_MANDATE, TransactionTypeEnum.SBMD_MANDATE,
			TransactionTypeEnum.ONE_TIME_MANDATE);

}
