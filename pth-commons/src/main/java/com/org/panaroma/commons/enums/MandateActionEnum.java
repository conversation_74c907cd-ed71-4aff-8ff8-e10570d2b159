package com.org.panaroma.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MandateActionEnum {

	CREATE(1), COLLECT(2), PAUSE(3), UNPAUSE(4), REVOKE(5), UPDATE(6),
	// Jira :- https://jira.mypaytm.com/browse/PTH-665
	EXPIRE(7),
	// New actions for mandate interoperability and porting
	IN_PORT(8), OUT_PORT(9);

	private final Integer mandateActionKey;

	public static MandateActionEnum getMandateActionEnumByName(final String txnPurpose) {
		for (MandateActionEnum mandateActionEnum : MandateActionEnum.values()) {
			if (mandateActionEnum.name().equalsIgnoreCase(txnPurpose)) {
				return mandateActionEnum;
			}
		}
		return null;
	}

	public static MandateActionEnum getMandateActionEnumByKey(final Integer mandateActionKey) {
		for (MandateActionEnum mandateActionEnum : MandateActionEnum.values()) {
			if (mandateActionEnum.mandateActionKey.equals(mandateActionKey)) {
				return mandateActionEnum;
			}
		}
		return null;
	}

}
