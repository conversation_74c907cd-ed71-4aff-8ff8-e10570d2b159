package com.org.panaroma.commons.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;

import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ReconKafkaObject implements Serializable {

	// name for the cron for the monitoring purpose.
	String name;

	// tag for flow identification
	BackFillingIdentifierEnum tag;

	// Whether to hit source or directly push data to retry pipeline.
	boolean isSourceCallRequired;

	SearchContext searchContext;

	PaginationParams paginationParams;

	@Override
	public String toString() {
		return "ReconKafkaObject{" + "name='" + name + '\'' + ", searchContext=" + searchContext + ", tag=" + tag
				+ ", paginationParams=" + paginationParams + '}';
	}

}
