package com.org.panaroma.commons.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateActionEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Flow Controller for Mandate Porting Operations
 * This class implements the pipeline-based approach where events are ingested from
 * the adaptor controller and processed through UPI and mandate pipelines.
 *
 * Event Flow: Adaptor Controller → Pipeline Router → UPI/Mandate Pipeline → PTH Processing
 */
public class MandatePortingFlowController {

    /**
     * Main pipeline router method that processes events from the adaptor controller.
     * Routes events to appropriate pipelines (UPI or Mandate) based on transaction type
     * and purpose codes, following the event-driven architecture.
     *
     * @param thd Transaction history details from adaptor controller
     * @return Processing result with pipeline routing decision
     */
    public static MandateFlowResult processTransaction(final TransactionHistoryDetails thd) {
        // Entry point - validate event from adaptor controller
        if (Objects.isNull(thd)) {
            return MandateFlowResult.createError("NULL_EVENT", "Event from adaptor controller is null");
        }

        // Pipeline Router: Determine which pipeline to use
        if (!TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
            // Route to UPI Pipeline for non-mandate transactions
            return MandateFlowResult.createStandardFlow("UPI_PIPELINE",
                "Routed to UPI pipeline for processing");
        }

        // Route to Mandate Pipeline - determine sub-pipeline based on purpose code
        String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(thd);

        // Mandate Pipeline Router: Route based on purpose code
        switch (purpose) {
            case PURPOSE_AZ:
                return handleInteroperabilityPipeline(thd);

            case PURPOSE_BC:
                return handlePortingPipeline(thd);

            case PURPOSE_COLLECT:
                return handleStandardMandatePipeline(thd);

            default:
                return MandateFlowResult.createStandardFlow("UNKNOWN_PURPOSE_PIPELINE",
                    "Unknown purpose code, using standard mandate pipeline: " + purpose);
        }
    }

    /**
     * Handles the Interoperability Pipeline for mandates with purpose AZ
     * Processes events for interoperable mandates that can be executed across UPI apps
     */
    private static MandateFlowResult handleInteroperabilityPipeline(final TransactionHistoryDetails thd) {
        String executionNo = thd.getContextMap() != null ? 
            thd.getContextMap().get("executionNo") : "1";

        // Pipeline Decision: Is this execution #1 (CREATE) or subsequent execution?
        if ("1".equals(executionNo)) {
            return MandateFlowResult.builder()
                .flowType(MandateFlowType.INTEROPERABLE_CREATE)
                .action(MandateActionEnum.CREATE)
                .routingDecision("INTEROPERABILITY_PIPELINE_CREATE")
                .message("Interoperable mandate creation via pipeline")
                .requiresPortingMetadata(false)
                .build();
        } else {
            return MandateFlowResult.builder()
                .flowType(MandateFlowType.INTEROPERABLE_EXECUTION)
                .action(MandateActionEnum.COLLECT)
                .routingDecision("INTEROPERABILITY_PIPELINE_EXECUTE")
                .message("Ported mandate execution via pipeline")
                .requiresPortingMetadata(true)
                .build();
        }
    }

    /**
     * Handles the Porting Pipeline for mandate porting operations (Purpose BC)
     * Processes events for mandate porting between UPI apps
     */
    private static MandateFlowResult handlePortingPipeline(final TransactionHistoryDetails thd) {
        if (Objects.isNull(thd.getContextMap())) {
            return MandateFlowResult.createError("MISSING_CONTEXT", "Context map required for porting");
        }

        String portType = thd.getContextMap().get("portType");

        // Pipeline Decision: Port IN or Port OUT processing?
        if (PURPOSE_IN_PORT.equals(portType)) {
            return handlePortInPipeline(thd);
        } else if (PURPOSE_OUT_PORT.equals(portType)) {
            return handlePortOutPipeline(thd);
        } else {
            return MandateFlowResult.createError("INVALID_PORT_TYPE",
                "Invalid port type for porting pipeline: " + portType);
        }
    }

    /**
     * Handles Port_IN pipeline processing - mandate being ported TO Paytm
     */
    private static MandateFlowResult handlePortInPipeline(final TransactionHistoryDetails thd) {
        // Validate required fields for incoming port
        if (!MandatePortingUtility.isValidPortingTransaction(thd)) {
            return MandateFlowResult.createError("INVALID_PORT_IN",
                "Invalid incoming port transaction in pipeline");
        }

        return MandateFlowResult.builder()
            .flowType(MandateFlowType.PORT_IN)
            .action(MandateActionEnum.IN_PORT)
            .routingDecision("PORTING_PIPELINE_IN")
            .message("Incoming port operation via porting pipeline")
            .requiresPortingMetadata(true)
            .build();
    }

    /**
     * Handles Port_OUT pipeline processing - mandate being ported FROM Paytm
     */
    private static MandateFlowResult handlePortOutPipeline(final TransactionHistoryDetails thd) {
        // Validate required fields for outgoing port
        if (!MandatePortingUtility.isValidPortingTransaction(thd)) {
            return MandateFlowResult.createError("INVALID_PORT_OUT",
                "Invalid outgoing port transaction in pipeline");
        }

        return MandateFlowResult.builder()
            .flowType(MandateFlowType.PORT_OUT)
            .action(MandateActionEnum.OUT_PORT)
            .routingDecision("PORTING_PIPELINE_OUT")
            .message("Outgoing port operation via porting pipeline")
            .requiresPortingMetadata(true)
            .build();
    }

    /**
     * Handles standard mandate pipeline processing (Purpose COLLECT)
     * Processes regular mandate execution events
     */
    private static MandateFlowResult handleStandardMandatePipeline(final TransactionHistoryDetails thd) {
        return MandateFlowResult.builder()
            .flowType(MandateFlowType.STANDARD)
            .action(MandateActionEnum.COLLECT)
            .routingDecision("STANDARD_MANDATE_PIPELINE")
            .message("Standard mandate execution via pipeline")
            .requiresPortingMetadata(false)
            .build();
    }

    /**
     * Flow types corresponding to the whiteboard diagram paths
     */
    public enum MandateFlowType {
        STANDARD,                    // Regular mandate execution
        INTEROPERABLE_CREATE,        // AZ purpose, execution #1
        INTEROPERABLE_EXECUTION,     // AZ purpose, execution >1
        PORT_IN,                     // BC purpose, IN_PORT
        PORT_OUT,                    // BC purpose, OUT_PORT
        ERROR                        // Error condition
    }

    /**
     * Result object that encapsulates the flow decision and routing information
     */
    public static class MandateFlowResult {
        private MandateFlowType flowType;
        private MandateActionEnum action;
        private String routingDecision;
        private String message;
        private boolean requiresPortingMetadata;
        private boolean isError;
        private String errorCode;

        // Builder pattern for easy construction
        public static MandateFlowResultBuilder builder() {
            return new MandateFlowResultBuilder();
        }

        public static MandateFlowResult createError(String errorCode, String message) {
            return builder()
                .flowType(MandateFlowType.ERROR)
                .isError(true)
                .errorCode(errorCode)
                .message(message)
                .build();
        }

        public static MandateFlowResult createStandardFlow(String routingDecision, String message) {
            return builder()
                .flowType(MandateFlowType.STANDARD)
                .action(MandateActionEnum.COLLECT)
                .routingDecision(routingDecision)
                .message(message)
                .requiresPortingMetadata(false)
                .build();
        }

        // Getters
        public MandateFlowType getFlowType() { return flowType; }
        public MandateActionEnum getAction() { return action; }
        public String getRoutingDecision() { return routingDecision; }
        public String getMessage() { return message; }
        public boolean isRequiresPortingMetadata() { return requiresPortingMetadata; }
        public boolean isError() { return isError; }
        public String getErrorCode() { return errorCode; }

        // Builder class
        public static class MandateFlowResultBuilder {
            private MandateFlowResult result = new MandateFlowResult();

            public MandateFlowResultBuilder flowType(MandateFlowType flowType) {
                result.flowType = flowType;
                return this;
            }

            public MandateFlowResultBuilder action(MandateActionEnum action) {
                result.action = action;
                return this;
            }

            public MandateFlowResultBuilder routingDecision(String routingDecision) {
                result.routingDecision = routingDecision;
                return this;
            }

            public MandateFlowResultBuilder message(String message) {
                result.message = message;
                return this;
            }

            public MandateFlowResultBuilder requiresPortingMetadata(boolean requiresPortingMetadata) {
                result.requiresPortingMetadata = requiresPortingMetadata;
                return this;
            }

            public MandateFlowResultBuilder isError(boolean isError) {
                result.isError = isError;
                return this;
            }

            public MandateFlowResultBuilder errorCode(String errorCode) {
                result.errorCode = errorCode;
                return this;
            }

            public MandateFlowResult build() {
                return result;
            }
        }
    }
}
