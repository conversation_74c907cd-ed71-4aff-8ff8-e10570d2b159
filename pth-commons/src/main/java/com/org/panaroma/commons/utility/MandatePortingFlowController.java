package com.org.panaroma.commons.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateActionEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * Flow Controller for Mandate Porting Operations
 * This class implements the flow-based approach as shown in the whiteboard diagram
 * for handling different mandate porting scenarios with decision-based routing.
 */
public class MandatePortingFlowController {

    /**
     * Main flow controller method that routes transactions based on the whiteboard flow diagram.
     * This implements the decision tree approach for mandate porting operations.
     * 
     * @param thd Transaction history details
     * @return Processing result with routing decision
     */
    public static MandateFlowResult processTransaction(final TransactionHistoryDetails thd) {
        // Entry point - validate input
        if (Objects.isNull(thd)) {
            return MandateFlowResult.createError("NULL_TRANSACTION", "Transaction is null");
        }

        // Decision Diamond 1: Is this a mandate transaction?
        if (!TransactionTypeEnum.RECURRING_MANDATE.equals(thd.getTxnType())) {
            return MandateFlowResult.createStandardFlow("NON_MANDATE", "Not a mandate transaction");
        }

        String purpose = com.org.panaroma.commons.utils.Utility.getPurpose(thd);
        
        // Decision Diamond 2: Route based on purpose code
        switch (purpose) {
            case PURPOSE_AZ:
                return handleInteroperableFlow(thd);
            
            case PURPOSE_BC:
                return handlePortingFlow(thd);
            
            case PURPOSE_COLLECT:
                return handleStandardFlow(thd);
            
            default:
                return MandateFlowResult.createStandardFlow("UNKNOWN_PURPOSE", 
                    "Unknown purpose code: " + purpose);
        }
    }

    /**
     * Handles the Execution flow for interoperable mandates (Purpose AZ)
     * Maps to the "Execution" path in the whiteboard diagram
     */
    private static MandateFlowResult handleInteroperableFlow(final TransactionHistoryDetails thd) {
        String executionNo = thd.getContextMap() != null ? 
            thd.getContextMap().get("executionNo") : "1";

        // Decision: Is this execution #1 (CREATE) or subsequent execution?
        if ("1".equals(executionNo)) {
            return MandateFlowResult.builder()
                .flowType(MandateFlowType.INTEROPERABLE_CREATE)
                .action(MandateActionEnum.CREATE)
                .routingDecision("EXECUTION_CREATE")
                .message("Interoperable mandate creation")
                .requiresPortingMetadata(false)
                .build();
        } else {
            return MandateFlowResult.builder()
                .flowType(MandateFlowType.INTEROPERABLE_EXECUTION)
                .action(MandateActionEnum.COLLECT)
                .routingDecision("EXECUTION_COLLECT")
                .message("Ported mandate execution")
                .requiresPortingMetadata(true)
                .build();
        }
    }

    /**
     * Handles the Port_IN/Port_OUT flow for porting operations (Purpose BC)
     * Maps to the "Port_IN" and "Port_OUT" paths in the whiteboard diagram
     */
    private static MandateFlowResult handlePortingFlow(final TransactionHistoryDetails thd) {
        if (Objects.isNull(thd.getContextMap())) {
            return MandateFlowResult.createError("MISSING_CONTEXT", "Context map required for porting");
        }

        String portType = thd.getContextMap().get("portType");
        
        // Decision Diamond: Port IN or Port OUT?
        if (PURPOSE_IN_PORT.equals(portType)) {
            return handlePortInFlow(thd);
        } else if (PURPOSE_OUT_PORT.equals(portType)) {
            return handlePortOutFlow(thd);
        } else {
            return MandateFlowResult.createError("INVALID_PORT_TYPE", 
                "Invalid port type: " + portType);
        }
    }

    /**
     * Handles Port_IN flow - mandate being ported TO Paytm
     */
    private static MandateFlowResult handlePortInFlow(final TransactionHistoryDetails thd) {
        // Validate required fields for incoming port
        if (!MandatePortingUtility.isValidPortingTransaction(thd)) {
            return MandateFlowResult.createError("INVALID_PORT_IN", 
                "Invalid incoming port transaction");
        }

        return MandateFlowResult.builder()
            .flowType(MandateFlowType.PORT_IN)
            .action(MandateActionEnum.IN_PORT)
            .routingDecision("PORT_IN_VALIDATED")
            .message("Incoming port operation")
            .requiresPortingMetadata(true)
            .build();
    }

    /**
     * Handles Port_OUT flow - mandate being ported FROM Paytm
     */
    private static MandateFlowResult handlePortOutFlow(final TransactionHistoryDetails thd) {
        // Validate required fields for outgoing port
        if (!MandatePortingUtility.isValidPortingTransaction(thd)) {
            return MandateFlowResult.createError("INVALID_PORT_OUT", 
                "Invalid outgoing port transaction");
        }

        return MandateFlowResult.builder()
            .flowType(MandateFlowType.PORT_OUT)
            .action(MandateActionEnum.OUT_PORT)
            .routingDecision("PORT_OUT_VALIDATED")
            .message("Outgoing port operation")
            .requiresPortingMetadata(true)
            .build();
    }

    /**
     * Handles standard mandate flow (Purpose COLLECT)
     * Maps to the standard execution path in the whiteboard diagram
     */
    private static MandateFlowResult handleStandardFlow(final TransactionHistoryDetails thd) {
        return MandateFlowResult.builder()
            .flowType(MandateFlowType.STANDARD)
            .action(MandateActionEnum.COLLECT)
            .routingDecision("STANDARD_EXECUTION")
            .message("Standard mandate execution")
            .requiresPortingMetadata(false)
            .build();
    }

    /**
     * Flow types corresponding to the whiteboard diagram paths
     */
    public enum MandateFlowType {
        STANDARD,                    // Regular mandate execution
        INTEROPERABLE_CREATE,        // AZ purpose, execution #1
        INTEROPERABLE_EXECUTION,     // AZ purpose, execution >1
        PORT_IN,                     // BC purpose, IN_PORT
        PORT_OUT,                    // BC purpose, OUT_PORT
        ERROR                        // Error condition
    }

    /**
     * Result object that encapsulates the flow decision and routing information
     */
    public static class MandateFlowResult {
        private MandateFlowType flowType;
        private MandateActionEnum action;
        private String routingDecision;
        private String message;
        private boolean requiresPortingMetadata;
        private boolean isError;
        private String errorCode;

        // Builder pattern for easy construction
        public static MandateFlowResultBuilder builder() {
            return new MandateFlowResultBuilder();
        }

        public static MandateFlowResult createError(String errorCode, String message) {
            return builder()
                .flowType(MandateFlowType.ERROR)
                .isError(true)
                .errorCode(errorCode)
                .message(message)
                .build();
        }

        public static MandateFlowResult createStandardFlow(String routingDecision, String message) {
            return builder()
                .flowType(MandateFlowType.STANDARD)
                .action(MandateActionEnum.COLLECT)
                .routingDecision(routingDecision)
                .message(message)
                .requiresPortingMetadata(false)
                .build();
        }

        // Getters
        public MandateFlowType getFlowType() { return flowType; }
        public MandateActionEnum getAction() { return action; }
        public String getRoutingDecision() { return routingDecision; }
        public String getMessage() { return message; }
        public boolean isRequiresPortingMetadata() { return requiresPortingMetadata; }
        public boolean isError() { return isError; }
        public String getErrorCode() { return errorCode; }

        // Builder class
        public static class MandateFlowResultBuilder {
            private MandateFlowResult result = new MandateFlowResult();

            public MandateFlowResultBuilder flowType(MandateFlowType flowType) {
                result.flowType = flowType;
                return this;
            }

            public MandateFlowResultBuilder action(MandateActionEnum action) {
                result.action = action;
                return this;
            }

            public MandateFlowResultBuilder routingDecision(String routingDecision) {
                result.routingDecision = routingDecision;
                return this;
            }

            public MandateFlowResultBuilder message(String message) {
                result.message = message;
                return this;
            }

            public MandateFlowResultBuilder requiresPortingMetadata(boolean requiresPortingMetadata) {
                result.requiresPortingMetadata = requiresPortingMetadata;
                return this;
            }

            public MandateFlowResultBuilder isError(boolean isError) {
                result.isError = isError;
                return this;
            }

            public MandateFlowResultBuilder errorCode(String errorCode) {
                result.errorCode = errorCode;
                return this;
            }

            public MandateFlowResult build() {
                return result;
            }
        }
    }
}
