package com.org.panaroma.commons.constants;

public class UthDtoContextMapKeyConstants {

	public static final String DCC_SRC_NAME = "dccSrcName";

	public static final String MERCHANT_NAME = "merchantName";

	public static final String DCC_ID = "dccId";

	public static final String MERCHANT_ID = "merchantId";

	public static final String DCC_TYPE = "dccType";

	public static final String REMITTER_MMID = "remitterMmid";

	public static final String CDEVICE_ID = "cdeviceId";

	public static final String FREE_FIELD8 = "freeField8";

	public static final String TXN_ID = "txnId";

	public static final String MCC_CODE = "mccCode";

	public static final String SUB_MERCHANT_ID = "subMerchantId";

	public static final String SUB_MERCHANT_NAME = "subMerchantName";

	public static final String TRANSACTION_ID = "transactionId";

	public static final String FREE_NUM2 = "freeNum2";

	public static final String ON_US_MERCHANT = "onUsMerchant";

	public static final String CARD_NETWORK = "cardNetwork";

	// Mandate porting related constants
	public static final String OTHER_UMN = "otherUMN";

	public static final String PREVIOUS_PAYER_APP = "previousPayerApp";

	public static final String PORT_TYPE = "portType";

	public static final String MANDATE_PORT_SEQUENCE = "mandatePortSequence";

}
