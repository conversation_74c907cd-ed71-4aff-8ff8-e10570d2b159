package com.org.panaroma.commons.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static org.junit.jupiter.api.Assertions.*;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.enums.MandateActionEnum;
import com.org.panaroma.commons.utility.MandatePortingFlowController.MandateFlowResult;
import com.org.panaroma.commons.utility.MandatePortingFlowController.MandateFlowType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;
import java.util.Map;

/**
 * Test class for MandatePortingFlowController
 * Tests the flow-based approach for mandate porting operations
 */
class MandatePortingFlowControllerTest {

    @Test
    @DisplayName("Should handle null transaction")
    void testNullTransaction() {
        MandateFlowResult result = MandatePortingFlowController.processTransaction(null);
        
        assertTrue(result.isError());
        assertEquals("NULL_TRANSACTION", result.getErrorCode());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should handle non-mandate transaction")
    void testNonMandateTransaction() {
        TransactionHistoryDetails thd = createBaseTransaction();
        thd.setTxnType(TransactionTypeEnum.DEBIT);
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.STANDARD, result.getFlowType());
        assertEquals("NON_MANDATE", result.getRoutingDecision());
    }

    @Test
    @DisplayName("Should handle interoperable mandate creation (AZ purpose, execution #1)")
    void testInteroperableMandateCreation() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_AZ);
        thd.getContextMap().put("executionNo", "1");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.INTEROPERABLE_CREATE, result.getFlowType());
        assertEquals(MandateActionEnum.CREATE, result.getAction());
        assertEquals("EXECUTION_CREATE", result.getRoutingDecision());
        assertFalse(result.isRequiresPortingMetadata());
    }

    @Test
    @DisplayName("Should handle interoperable mandate execution (AZ purpose, execution >1)")
    void testInteroperableMandateExecution() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_AZ);
        thd.getContextMap().put("executionNo", "2");
        thd.getContextMap().put("otherUMN", "12345@phonepe");
        thd.getContextMap().put("previousPayerApp", "phonepe");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.INTEROPERABLE_EXECUTION, result.getFlowType());
        assertEquals(MandateActionEnum.COLLECT, result.getAction());
        assertEquals("EXECUTION_COLLECT", result.getRoutingDecision());
        assertTrue(result.isRequiresPortingMetadata());
    }

    @Test
    @DisplayName("Should handle incoming port operation (BC purpose, IN_PORT)")
    void testIncomingPortOperation() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_BC);
        thd.getContextMap().put("portType", PURPOSE_IN_PORT);
        thd.getContextMap().put("otherUMN", "67890@phonepe");
        thd.getContextMap().put("previousPayerApp", "phonepe");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.PORT_IN, result.getFlowType());
        assertEquals(MandateActionEnum.IN_PORT, result.getAction());
        assertEquals("PORT_IN_VALIDATED", result.getRoutingDecision());
        assertTrue(result.isRequiresPortingMetadata());
    }

    @Test
    @DisplayName("Should handle outgoing port operation (BC purpose, OUT_PORT)")
    void testOutgoingPortOperation() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_BC);
        thd.getContextMap().put("portType", PURPOSE_OUT_PORT);
        thd.getContextMap().put("otherUMN", "54321@paytm");
        thd.getContextMap().put("previousPayerApp", "paytm");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.PORT_OUT, result.getFlowType());
        assertEquals(MandateActionEnum.OUT_PORT, result.getAction());
        assertEquals("PORT_OUT_VALIDATED", result.getRoutingDecision());
        assertTrue(result.isRequiresPortingMetadata());
    }

    @Test
    @DisplayName("Should handle standard mandate execution (COLLECT purpose)")
    void testStandardMandateExecution() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_COLLECT);
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.STANDARD, result.getFlowType());
        assertEquals(MandateActionEnum.COLLECT, result.getAction());
        assertEquals("STANDARD_EXECUTION", result.getRoutingDecision());
        assertFalse(result.isRequiresPortingMetadata());
    }

    @Test
    @DisplayName("Should handle unknown purpose code")
    void testUnknownPurposeCode() {
        TransactionHistoryDetails thd = createMandateTransaction("UNKNOWN");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.STANDARD, result.getFlowType());
        assertEquals("UNKNOWN_PURPOSE", result.getRoutingDecision());
        assertTrue(result.getMessage().contains("Unknown purpose code: UNKNOWN"));
    }

    @Test
    @DisplayName("Should handle porting transaction without context map")
    void testPortingTransactionWithoutContext() {
        TransactionHistoryDetails thd = createBaseTransaction();
        thd.setTxnType(TransactionTypeEnum.RECURRING_MANDATE);
        thd.setPurpose(PURPOSE_BC);
        thd.setContextMap(null);
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertTrue(result.isError());
        assertEquals("MISSING_CONTEXT", result.getErrorCode());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should handle invalid port type")
    void testInvalidPortType() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_BC);
        thd.getContextMap().put("portType", "INVALID_TYPE");
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertTrue(result.isError());
        assertEquals("INVALID_PORT_TYPE", result.getErrorCode());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should handle invalid incoming port transaction")
    void testInvalidIncomingPortTransaction() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_BC);
        thd.getContextMap().put("portType", PURPOSE_IN_PORT);
        // Missing required porting fields
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertTrue(result.isError());
        assertEquals("INVALID_PORT_IN", result.getErrorCode());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should handle invalid outgoing port transaction")
    void testInvalidOutgoingPortTransaction() {
        TransactionHistoryDetails thd = createMandateTransaction(PURPOSE_BC);
        thd.getContextMap().put("portType", PURPOSE_OUT_PORT);
        // Missing required porting fields
        
        MandateFlowResult result = MandatePortingFlowController.processTransaction(thd);
        
        assertTrue(result.isError());
        assertEquals("INVALID_PORT_OUT", result.getErrorCode());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should test flow result builder pattern")
    void testFlowResultBuilder() {
        MandateFlowResult result = MandateFlowResult.builder()
            .flowType(MandateFlowType.INTEROPERABLE_CREATE)
            .action(MandateActionEnum.CREATE)
            .routingDecision("TEST_DECISION")
            .message("Test message")
            .requiresPortingMetadata(true)
            .build();
        
        assertEquals(MandateFlowType.INTEROPERABLE_CREATE, result.getFlowType());
        assertEquals(MandateActionEnum.CREATE, result.getAction());
        assertEquals("TEST_DECISION", result.getRoutingDecision());
        assertEquals("Test message", result.getMessage());
        assertTrue(result.isRequiresPortingMetadata());
        assertFalse(result.isError());
    }

    @Test
    @DisplayName("Should test error result creation")
    void testErrorResultCreation() {
        MandateFlowResult result = MandateFlowResult.createError("TEST_ERROR", "Test error message");
        
        assertTrue(result.isError());
        assertEquals("TEST_ERROR", result.getErrorCode());
        assertEquals("Test error message", result.getMessage());
        assertEquals(MandateFlowType.ERROR, result.getFlowType());
    }

    @Test
    @DisplayName("Should test standard flow result creation")
    void testStandardFlowResultCreation() {
        MandateFlowResult result = MandateFlowResult.createStandardFlow("TEST_STANDARD", "Standard flow");
        
        assertFalse(result.isError());
        assertEquals(MandateFlowType.STANDARD, result.getFlowType());
        assertEquals(MandateActionEnum.COLLECT, result.getAction());
        assertEquals("TEST_STANDARD", result.getRoutingDecision());
        assertEquals("Standard flow", result.getMessage());
        assertFalse(result.isRequiresPortingMetadata());
    }

    // Helper methods
    private TransactionHistoryDetails createBaseTransaction() {
        TransactionHistoryDetails thd = new TransactionHistoryDetails();
        thd.setContextMap(new HashMap<>());
        return thd;
    }

    private TransactionHistoryDetails createMandateTransaction(String purpose) {
        TransactionHistoryDetails thd = createBaseTransaction();
        thd.setTxnType(TransactionTypeEnum.RECURRING_MANDATE);
        thd.setPurpose(purpose);
        return thd;
    }
}
