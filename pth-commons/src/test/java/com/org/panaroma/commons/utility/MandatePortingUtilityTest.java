package com.org.panaroma.commons.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_BC;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_IN_PORT;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_OUT_PORT;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.OTHER_UMN;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.PREVIOUS_PAYER_APP;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.PORT_TYPE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.MandateActionEnum;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * Test class for MandatePortingUtility.
 */
public class MandatePortingUtilityTest {

	@Test
	public void testIsInteroperableMandate() {
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_AZ,
				TransactionTypeEnum.RECURRING_MANDATE);
		assertTrue(MandatePortingUtility.isInteroperableMandate(thd));

		// Test with non-AZ purpose
		thd = createTransactionHistoryDetails(PURPOSE_COLLECT, TransactionTypeEnum.RECURRING_MANDATE);
		assertFalse(MandatePortingUtility.isInteroperableMandate(thd));

		// Test with null
		assertFalse(MandatePortingUtility.isInteroperableMandate(null));
	}

	@Test
	public void testIsMandatePortingTransaction() {
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_BC,
				TransactionTypeEnum.RECURRING_MANDATE);
		assertTrue(MandatePortingUtility.isMandatePortingTransaction(thd));

		// Test with non-BC purpose
		thd = createTransactionHistoryDetails(PURPOSE_AZ, TransactionTypeEnum.RECURRING_MANDATE);
		assertFalse(MandatePortingUtility.isMandatePortingTransaction(thd));
	}

	@Test
	public void testIsIncomingPort() {
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_BC,
				TransactionTypeEnum.RECURRING_MANDATE);
		thd.getContextMap().put(PORT_TYPE, PURPOSE_IN_PORT);
		assertTrue(MandatePortingUtility.isIncomingPort(thd));

		// Test with OUT_PORT
		thd.getContextMap().put(PORT_TYPE, PURPOSE_OUT_PORT);
		assertFalse(MandatePortingUtility.isIncomingPort(thd));
	}

	@Test
	public void testIsOutgoingPort() {
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_BC,
				TransactionTypeEnum.RECURRING_MANDATE);
		thd.getContextMap().put(PORT_TYPE, PURPOSE_OUT_PORT);
		assertTrue(MandatePortingUtility.isOutgoingPort(thd));

		// Test with IN_PORT
		thd.getContextMap().put(PORT_TYPE, PURPOSE_IN_PORT);
		assertFalse(MandatePortingUtility.isOutgoingPort(thd));
	}

	@Test
	public void testIsPortedMandate() {
		TransformedTransactionHistoryDetail tthd = createTransformedTransactionHistoryDetail(PURPOSE_AZ);
		tthd.getContextMap().put("executionNo", "2");
		assertTrue(MandatePortingUtility.isPortedMandate(tthd));

		// Test with execution number 1 (not ported)
		tthd.getContextMap().put("executionNo", "1");
		assertFalse(MandatePortingUtility.isPortedMandate(tthd));

		// Test with non-AZ purpose
		tthd = createTransformedTransactionHistoryDetail(PURPOSE_COLLECT);
		tthd.getContextMap().put("executionNo", "2");
		assertFalse(MandatePortingUtility.isPortedMandate(tthd));
	}

	@Test
	public void testExtractPayerAppFromUMN() {
		String umn = "12345@paytm";
		assertEquals("paytm", MandatePortingUtility.extractPayerAppFromUMN(umn));

		// Test with invalid UMN
		assertNull(MandatePortingUtility.extractPayerAppFromUMN("invalid"));
		assertNull(MandatePortingUtility.extractPayerAppFromUMN(null));
		assertNull(MandatePortingUtility.extractPayerAppFromUMN(""));
	}

	@Test
	public void testGetPortingMandateAction() {
		// Test IN_PORT
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_BC,
				TransactionTypeEnum.RECURRING_MANDATE);
		thd.getContextMap().put(PORT_TYPE, PURPOSE_IN_PORT);
		assertEquals(MandateActionEnum.IN_PORT, MandatePortingUtility.getPortingMandateAction(thd));

		// Test OUT_PORT
		thd.getContextMap().put(PORT_TYPE, PURPOSE_OUT_PORT);
		assertEquals(MandateActionEnum.OUT_PORT, MandatePortingUtility.getPortingMandateAction(thd));

		// Test interoperable mandate
		thd = createTransactionHistoryDetails(PURPOSE_AZ, TransactionTypeEnum.RECURRING_MANDATE);
		assertEquals(MandateActionEnum.COLLECT, MandatePortingUtility.getPortingMandateAction(thd));
	}

	@Test
	public void testIsValidPortingTransaction() {
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_BC,
				TransactionTypeEnum.RECURRING_MANDATE);
		thd.getContextMap().put(PORT_TYPE, PURPOSE_IN_PORT);
		thd.getContextMap().put(OTHER_UMN, "12345@paytm");
		assertTrue(MandatePortingUtility.isValidPortingTransaction(thd));

		// Test without other UMN
		thd.getContextMap().remove(OTHER_UMN);
		assertFalse(MandatePortingUtility.isValidPortingTransaction(thd));

		// Test without port type
		thd.getContextMap().put(OTHER_UMN, "12345@paytm");
		thd.getContextMap().remove(PORT_TYPE);
		assertFalse(MandatePortingUtility.isValidPortingTransaction(thd));
	}

	@Test
	public void testGetPayerAppDisplayName() {
		assertEquals("Paytm", MandatePortingUtility.getPayerAppDisplayName("paytm"));
		assertEquals("PhonePe", MandatePortingUtility.getPayerAppDisplayName("ybl"));
		assertEquals("HDFC Bank", MandatePortingUtility.getPayerAppDisplayName("pthdfc"));
		assertEquals("UNKNOWN UPI App", MandatePortingUtility.getPayerAppDisplayName("unknown"));
		assertEquals("Previous UPI App", MandatePortingUtility.getPayerAppDisplayName(null));
		assertEquals("Previous UPI App", MandatePortingUtility.getPayerAppDisplayName(""));
	}

	@Test
	public void testShouldProcessForInteroperability() {
		// Test interoperable mandate
		TransactionHistoryDetails thd = createTransactionHistoryDetails(PURPOSE_AZ,
				TransactionTypeEnum.RECURRING_MANDATE);
		assertTrue(MandatePortingUtility.shouldProcessForInteroperability(thd));

		// Test porting transaction
		thd = createTransactionHistoryDetails(PURPOSE_BC, TransactionTypeEnum.RECURRING_MANDATE);
		assertTrue(MandatePortingUtility.shouldProcessForInteroperability(thd));

		// Test regular mandate
		thd = createTransactionHistoryDetails(PURPOSE_COLLECT, TransactionTypeEnum.RECURRING_MANDATE);
		assertFalse(MandatePortingUtility.shouldProcessForInteroperability(thd));
	}

	private TransactionHistoryDetails createTransactionHistoryDetails(String purpose, TransactionTypeEnum txnType) {
		TransactionHistoryDetails thd = new TransactionHistoryDetails();
		thd.setTxnType(txnType);

		Map<String, String> contextMap = new HashMap<>();
		contextMap.put("purpose", purpose);
		thd.setContextMap(contextMap);

		return thd;
	}

	private TransformedTransactionHistoryDetail createTransformedTransactionHistoryDetail(String purpose) {
		TransformedTransactionHistoryDetail tthd = new TransformedTransactionHistoryDetail();
		tthd.setTxnType(TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey());

		Map<String, String> contextMap = new HashMap<>();
		contextMap.put("purpose", purpose);
		tthd.setContextMap(contextMap);

		return tthd;
	}

}
