package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_COLLECT;
import static com.org.panaroma.commons.constants.WebConstants.AMPERSAND;
import static com.org.panaroma.commons.constants.WebConstants.BANK_NAME_PARAM;
import static com.org.panaroma.commons.constants.WebConstants.DEEPLINK;
import static com.org.panaroma.commons.constants.WebConstants.EQUAL_SYMBOL;
import static com.org.panaroma.commons.constants.WebConstants.MANDATE_TXN_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.AUTOMATIC_PAYMENT;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.AUTOMATIC_PAYMENT_SETUP;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.EXECUTION_NO_ONE;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.FOR;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MANAGE_AUTOMATIC_PAYMENT_CTA_DEEPLINK;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.MASKED_ACCOUNT_NO;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.PAYEENAME;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.RECURRING_AUTOMATIC_PAYMENT_CTA_LABEL;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.SENT_FROM;
import static com.org.panaroma.commons.constants.WebConstants.MandateConstants.SETUP;
import static com.org.panaroma.commons.constants.WebConstants.SPACE;
import static com.org.panaroma.commons.constants.WebConstants.SPACE_REPLACEMENT;
import static com.org.panaroma.commons.constants.WebConstants.TRANSACTION_PURPOSE;
import static com.org.panaroma.commons.constants.CommonConstants.PURPOSE_AZ;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.OTHER_UMN;
import static com.org.panaroma.commons.constants.UthDtoContextMapKeyConstants.PREVIOUS_PAYER_APP;
import static com.org.panaroma.commons.utils.Utility.getLastFourDigitOfAccountNo;
import static com.org.panaroma.web.utility.GenericUtilityExtension.isRecurringMandateAndUpiCc;

import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.CtaType;
import com.org.panaroma.commons.enums.CtaValueType;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.CtaNode;
import com.org.panaroma.web.dto.detailAPI.detailResponseV2.DetailApiResponseV2;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import java.util.Collections;
import java.util.HashMap;
import java.util.Objects;

import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class MandateUtility {

	private static String recurringMandateH5BaseUrl;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Autowired
	public MandateUtility(@Value("${recurringMandate.h5.base.url}") final String recurringMandateH5BaseUrl,
			final ConfigurablePropertiesHolder configurablePropertiesHolder) {
		MandateUtility.recurringMandateH5BaseUrl = recurringMandateH5BaseUrl;
		MandateUtility.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public static void setListingNarrationForRecurringMandate(final TransformedTransactionHistoryDetail tthd,
			final EsResponseTxn listingResponse) {
		if (Objects.isNull(tthd)) {
			return;
		}
		Boolean flag = false;
		if (Objects.nonNull(tthd.getContextMap())) {
			flag = tthd.getContextMap().containsKey(MANDATE_TXN_TYPE);
		}
		if (TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(tthd.getTxnType())
				|| (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(tthd.getTxnType()) && flag)) {

			listingResponse.setUserInstrumentNarration(SENT_FROM);
			listingResponse.setUserInstrumentLogosV2(listingResponse.getUserInstrumentLogos());
			String merchantName = null;
			for (TransformedParticipant participant : tthd.getParticipants()) {
				if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
					merchantName = participant.getName();
				}
			}
			if (Objects.nonNull(tthd.getContextMap())) {
				String executionNo = GenericUtilityExtension.getExecutionNo(tthd);
				String mandateAmount = GenericUtilityExtension.getMandateAmount(tthd);
				String txnPurpose = GenericUtilityExtension.getTransactionPurpose(tthd);

				if (checkValidityOfField(executionNo, mandateAmount, txnPurpose)) {
					String narration = null;
					ListingResponseMappingEnum listingResponseMappingEnum;

					// Check if this is a ported mandate (interoperable with purpose AZ)
					boolean isPortedMandate = PURPOSE_AZ.equals(txnPurpose) && !EXECUTION_NO_ONE.equals(executionNo);

					if (EXECUTION_NO_ONE.equals(executionNo)) {
						if (StringUtils.isBlank(merchantName)) {
							narration = AUTOMATIC_PAYMENT_SETUP + mandateAmount + SETUP;
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_SETUP_DEFAULT;
						}
						else {
							narration = AUTOMATIC_PAYMENT_SETUP + mandateAmount + SETUP + FOR;
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_SETUP;
						}
						listingResponse.setNarration(narration);
					}
					else if (isPortedMandate) {
						// Handle ported mandate narration
						String previousPayerApp = getPreviousPayerAppForNarration(tthd);
						if (StringUtils.isBlank(merchantName)) {
							narration = "Automatic payment of ₹" + mandateAmount + " (ported from " + previousPayerApp
									+ ")";
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_SETUP_DEFAULT;
						}
						else {
							narration = "Automatic payment of ₹" + mandateAmount + " (ported from " + previousPayerApp
									+ ") for";
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_SETUP;
						}
						listingResponse.setNarration(narration);
					}
					else {
						if (StringUtils.isBlank(merchantName)) {
							narration = AUTOMATIC_PAYMENT;
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_PAYMENT_DEFAULT;
						}
						else {
							narration = AUTOMATIC_PAYMENT + FOR;
							listingResponseMappingEnum = ListingResponseMappingEnum.RECURRING_MANDATE_PAYMENT;
						}
					}
					listingResponse.setNarration(narration);
					listingResponse.setListingResponseMappingEnum(listingResponseMappingEnum);
				}
			}
		}
	}

	private static boolean checkValidityOfField(final String executionNo, final String mandateAmount,
			final String txnPurpose) {
		if (Objects.isNull(executionNo)) {
			return false;
		}
		if (Objects.isNull(mandateAmount)) {
			return false;
		}
		if (Objects.isNull(txnPurpose)) {
			return false;
		}
		// Allow both COLLECT and AZ purpose codes for mandate processing
		if (ObjectUtils.notEqual(txnPurpose, PURPOSE_COLLECT) && ObjectUtils.notEqual(txnPurpose, PURPOSE_AZ)) {
			return false;
		}
		return true;
	}

	/**
	 * Gets the previous payer app name for narration purposes in ported mandates.
	 * @param tthd Transaction history detail
	 * @return Previous payer app display name
	 */
	private static String getPreviousPayerAppForNarration(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.isNull(tthd) || Objects.isNull(tthd.getContextMap())) {
			return "Previous UPI App";
		}

		// First try to get from context map
		String previousPayerApp = tthd.getContextMap().get(PREVIOUS_PAYER_APP);
		if (StringUtils.isNotBlank(previousPayerApp)) {
			return com.org.panaroma.commons.utility.MandatePortingUtility.getPayerAppDisplayName(previousPayerApp);
		}

		// Try to extract from other UMN
		String otherUMN = tthd.getContextMap().get(OTHER_UMN);
		if (StringUtils.isNotBlank(otherUMN)) {
			String extractedApp = com.org.panaroma.commons.utility.MandatePortingUtility
				.extractPayerAppFromUMN(otherUMN);
			return com.org.panaroma.commons.utility.MandatePortingUtility.getPayerAppDisplayName(extractedApp);
		}

		return "Previous UPI App";
	}

	public static Boolean dateTimeLableValidityForCreate(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd.getContextMap())) {
			String executionNo = GenericUtilityExtension.getExecutionNo(tthd);
			String txnPurpose = GenericUtilityExtension.getTransactionPurpose(tthd);
			Boolean flag = false;
			if (Objects.nonNull(tthd.getContextMap())) {
				flag = tthd.getContextMap().containsKey(MANDATE_TXN_TYPE);
			}
			if (PURPOSE_COLLECT.equals(txnPurpose) || flag) {
				if (EXECUTION_NO_ONE.equals(executionNo)) {
					return true;
				}
			}
		}
		return false;
	}

	public static Boolean dateTimeLableValidityForCollect(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd.getContextMap())) {
			String executionNo = GenericUtilityExtension.getExecutionNo(tthd);
			String txnPurpose = GenericUtilityExtension.getTransactionPurpose(tthd);
			Boolean flag = false;
			if (Objects.nonNull(tthd.getContextMap())) {
				flag = tthd.getContextMap().containsKey(MANDATE_TXN_TYPE);
			}
			if (PURPOSE_COLLECT.equals(txnPurpose) || flag) {
				if (!EXECUTION_NO_ONE.equals(executionNo)) {
					return true;
				}
			}
		}
		return false;
	}

	public static void addManageAutomaticPaymentCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		if (TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				|| (Objects.nonNull(listingVisibleTxn.getContextMap())
						&& listingVisibleTxn.getContextMap().containsKey(MANDATE_TXN_TYPE))) {
			createManageAutomaticPaymentCtaNode(detailApiResponseV2, listingVisibleTxn, label);
		}
	}

	public static boolean txnValidForRecurring(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if ((TransactionTypeEnum.RECURRING_MANDATE.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				|| TransactionTypeEnum.LITE_TOPUP_MANDATE.getTransactionTypeKey()
					.equals(listingVisibleTxn.getTxnType()))
				&& listingVisibleTxn.getContextMap().containsKey(TRANSACTION_PURPOSE)
				&& listingVisibleTxn.getContextMap().containsKey(EXECUTION_NO)) {
			return true;
		}
		else if (TransactionTypeEnum.P2M.getTransactionTypeKey().equals(listingVisibleTxn.getTxnType())
				&& listingVisibleTxn.getContextMap().containsKey(MANDATE_TXN_TYPE)
				&& TransactionTypeEnum.RECURRING_MANDATE.getTransactionType()
					.equals(listingVisibleTxn.getContextMap().get(MANDATE_TXN_TYPE))
				&& listingVisibleTxn.getContextMap().containsKey(TRANSACTION_PURPOSE)
				&& listingVisibleTxn.getContextMap().containsKey(EXECUTION_NO)) {
			return true;
		}
		else {
			return false;
		}
	}

	public static CtaNode createRecurringMandateH5CtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn,
			final String label) {
		String deepLink = getRecurringMandateH5DeepLink(listingVisibleTxn);

		if (StringUtils.isBlank(deepLink)) {
			return null;
		}

		CtaNode ctaNode = new CtaNode();
		ctaNode.setCtaType(CtaType.DESCRIPTION.getCtaType());
		ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
		ctaNode.setLabel(label);
		ctaNode.setValue(Collections.singletonMap(DEEPLINK, deepLink));
		return ctaNode;
	}

	// This method create deeplink for recurring mandate H5 page.
	public static String getRecurringMandateH5DeepLink(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		if (StringUtils.isBlank(recurringMandateH5BaseUrl)) {
			return null;
		}
		String payeename = "";
		String bankName = "";
		String maskedAccountNum = "";
		for (TransformedParticipant participant : listingVisibleTxn.getParticipants()) {
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				payeename = participant.getName();
			}
			else if (Objects.nonNull(participant.getBankData())) {
				bankName = participant.getBankData().getBankName();
				maskedAccountNum = getLastFourDigitOfAccountNo(participant.getBankData().getAccNumber());
			}
		}

		if (StringUtils.isBlank(payeename) || StringUtils.isBlank(bankName) || StringUtils.isBlank(maskedAccountNum)) {
			return null;
		}

		String recurringH5Cta = recurringMandateH5BaseUrl + AMPERSAND + PAYEENAME + EQUAL_SYMBOL + payeename + AMPERSAND
				+ BANK_NAME_PARAM + EQUAL_SYMBOL + bankName + AMPERSAND + MASKED_ACCOUNT_NO + EQUAL_SYMBOL
				+ maskedAccountNum;
		recurringH5Cta = recurringH5Cta.replaceAll(SPACE, SPACE_REPLACEMENT);
		return recurringH5Cta;
	}

	public static CtaNode getDetailRecurringCtaNode(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String ctaLabel;

		if (isRecurringMandateTxn(listingVisibleTxn)) {
			ctaLabel = RECURRING_AUTOMATIC_PAYMENT_CTA_LABEL;
		}
		else {
			return null;
		}

		return createRecurringMandateH5CtaNode(listingVisibleTxn, ctaLabel);
	}

	public static boolean isRecurringMandateTxn(final TransformedTransactionHistoryDetail listingVisibleTxn) {
		String txnPurpose = GenericUtilityExtension.getTransactionPurpose(listingVisibleTxn);
		String executionNo = GenericUtilityExtension.getExecutionNo(listingVisibleTxn);
		return PURPOSE_COLLECT.equals(txnPurpose) && Objects.equals(executionNo, EXECUTION_NO_ONE);
	}

	// This method creates "Manage Automatic Payment" CTA in case of RecurringMandate's
	// Success txn
	public static void createManageAutomaticPaymentCtaNode(final DetailApiResponseV2 detailApiResponseV2,
			final TransformedTransactionHistoryDetail listingVisibleTxn, final String label) {
		try {
			if (Objects.nonNull(listingVisibleTxn) && Objects.nonNull(listingVisibleTxn.getUmn())) {
				{
					CtaNode ctaNode = new CtaNode();
					ctaNode.setCtaType(CtaType.MANAGE_AUTOMATIC_PAYMENT.getCtaType());
					ctaNode.setLabel(label);
					ctaNode.setValueType(CtaValueType.LINK.getCtaValueType());
					String manageAutomaticPaymentDeeplink = configurablePropertiesHolder
						.getProperty(MANAGE_AUTOMATIC_PAYMENT_CTA_DEEPLINK, String.class) + listingVisibleTxn.getUmn();
					ctaNode.setValue(Collections.singletonMap(DEEPLINK, manageAutomaticPaymentDeeplink));

					if (Objects.isNull(detailApiResponseV2.getCtasMap())) {
						detailApiResponseV2.setCtasMap(new HashMap<>());
					}
					detailApiResponseV2.getCtasMap().put(CtaType.MANAGE_AUTOMATIC_PAYMENT.getCtaType(), ctaNode);
				}
			}
		}
		catch (NullPointerException ex) {
			log.error("null pointer exception not able to get UMN for txnId {}", listingVisibleTxn.getTxnId());
			return;
		}
	}

}
