package com.org.panaroma.ingester.transformer;

import static com.org.panaroma.commons.constants.CommonConstants.BACKFILLING_IDENTIFIER;
import static com.org.panaroma.commons.constants.CommonConstants.RECON_CONFIG_KAFKA_CLIENT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.ONLINE_ONUS_NATIVE;
import static com.org.panaroma.ingester.constants.Constants.COLON;
import static com.org.panaroma.ingester.constants.Constants.NAME;
import static com.org.panaroma.ingester.constants.Constants.RETRY_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.IGNORE_DTOS_ARE_SAME;

import com.org.panaroma.commons.constants.CommonConstants;
import com.org.panaroma.commons.dto.ReconKafkaObject;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.BackFillingIdentifierEnum;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.kafka.KafkaUtility;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.IndexUtility;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.monitoring.MonitoringConstants;
import com.org.panaroma.ingester.repository.IRepository;
import com.org.panaroma.ingester.utils.RetryUtility;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class ReconConfigDataProcessor
		implements FlatMapFunction<ReconKafkaObject, TransformedTransactionHistoryDetail>, Serializable {

	private IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> repository;

	private IKafkaClient iKafkaClient;

	private MetricsAgent metricsAgent;

	private RetryUtility retryUtility;

	@Autowired
	public ReconConfigDataProcessor(
			final IRepository<TransformedTransactionHistoryDetail, Map<String, Object>> repository,
			final IKafkaClient ikafkaClient, final MetricsAgent metricsAgent, final RetryUtility retryUtility) {
		this.repository = repository;
		this.iKafkaClient = ikafkaClient;
		this.metricsAgent = metricsAgent;
		this.retryUtility = retryUtility;
	}

	@Override
	public void flatMap(final ReconKafkaObject reconKafkaObject,
			final Collector<TransformedTransactionHistoryDetail> collector) {
		try {
			log.warn("Found kafka stream for recon pipeline on MSK : {}", reconKafkaObject);
			if (Objects.isNull(reconKafkaObject) || Objects.isNull(reconKafkaObject.getSearchContext())
					|| StringUtils.isBlank(reconKafkaObject.getName()) || Objects.isNull(reconKafkaObject.getTag())) {
				return;
			}

			List<TransformedTransactionHistoryDetail> detailSet;
			detailSet = this.repository.fetchData(reconKafkaObject.getSearchContext(),
					reconKafkaObject.getPaginationParams(), true);
			if (detailSet != null && !detailSet.isEmpty()) {
				log.info("Total fetched record for tag :{}, name : {}, size:{}", reconKafkaObject.getTag(),
						reconKafkaObject.getName(), detailSet.size());
				metricsAgent.recordCronDataSize(MonitoringConstants.TXN_COUNT_PER_CRON_TAG, (long) detailSet.size(),
						reconKafkaObject.getTag().name(), reconKafkaObject.getName(),
						IndexUtility.getDateFromEpochTime(
								String.valueOf(reconKafkaObject.getSearchContext().getFromDate()), null),
						IndexUtility.getDateFromEpochTime(
								String.valueOf(reconKafkaObject.getSearchContext().getToDate()), null));
				if (detailSet.size() == reconKafkaObject.getSearchContext().getPageSize() + 1) {
					ReconKafkaObject updatedReconKafkaObject = KafkaUtility.getUpdatedReconKafkaObject(reconKafkaObject,
							detailSet);
					log.info("updated record to be pushed into kafka for next batch :{}", updatedReconKafkaObject);
					this.pushDataIntoKakfka(updatedReconKafkaObject);
				}

				PushDataToRetryOrSink(reconKafkaObject, collector, detailSet);
			}
		}
		catch (Exception e) {
			// repush into kafka the same object
			log.error("Exception while processing the data. Exception :{}", CommonsUtility.exceptionFormatter(e));
			// push metric for this
			this.pushDataIntoKakfka(reconKafkaObject);
		}
	}

	private void PushDataToRetryOrSink(final ReconKafkaObject reconKafkaObject,
			final Collector<TransformedTransactionHistoryDetail> collector,
			final List<TransformedTransactionHistoryDetail> detailSet) {
		for (TransformedTransactionHistoryDetail detail : detailSet) {
			Map<String, String> contextMap = detail.getContextMap();
			if (Objects.isNull(contextMap)) {
				contextMap = new HashMap<>();
				detail.setContextMap(contextMap);
			}
			detail.getContextMap().put(BACKFILLING_IDENTIFIER, reconKafkaObject.getTag().getBackFillingIdentifierKey());
			metricsAgent.incrementCount(reconKafkaObject.getTag().name(), NAME + COLON + reconKafkaObject.getName());

			if (reconKafkaObject.isSourceCallRequired()) {
				log.info("Pushing data into status-resolver pipeline for name : {}, tag :{}, txnId :{}",
						reconKafkaObject.getName(), reconKafkaObject.getTag(), detail.getTxnId());
				collector.collect(detail);
			}
			else {
				log.info("Pushing data into retry pipeline for name : {}, tag :{}, txnId :{}",
						reconKafkaObject.getName(), reconKafkaObject.getTag(), detail.getTxnId());

				// Added Ignore DTOs share the same flag; otherwise, these events will be
				// ignored in UPI pipeline processing.
				detail.getContextMap().put(IGNORE_DTOS_ARE_SAME, CommonConstants.TRUE);

				// Special check for UPI_ONUS_PENDING_AFTER_N_DAYS recon.
				if (BackFillingIdentifierEnum.UPI_ONUS_PENDING_AFTER_N_DAYS.equals(reconKafkaObject.getTag())
						&& Objects.nonNull(detail.getContextMap()) && Boolean.TRUE.equals(StringUtils
							.equalsIgnoreCase(detail.getContextMap().get(CHANNEL_CODE), ONLINE_ONUS_NATIVE))) {
					detail.setSourceSystem(null);
					detail.setSourceTxnId(null);
					detail.setStatus(detail.getOriginalStatus());
				}
				retryUtility.pushDataToKafka(RETRY_PIPELINE, detail);
			}
		}
	}

	private void pushDataIntoKakfka(final ReconKafkaObject reconKafkaObject) {
		try {
			this.iKafkaClient.pushIntoKafkaInSync(RECON_CONFIG_KAFKA_CLIENT_NAME, reconKafkaObject.getName(),
					reconKafkaObject);
		}
		catch (Exception ex) {
			log.error("Exception :{} while pushing data into kafka for systemId:{}",
					CommonsUtility.exceptionFormatter(ex), reconKafkaObject);
		}

	}

}
