package com.org.com.webapiv2.utility.boPanel;

import com.org.com.webapiv2.dto.BmsPropertiesDto;
import com.org.com.webapiv2.dto.BmsServicePropertiesUpdateDto;
import com.org.com.webapiv2.util.TestUtils;
import lombok.extern.log4j.Log4j2;
import org.junit.Assert;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
@Log4j2
public class BoPanelUtilityTest {

	@Autowired
	BmsRestClientServiceUnifiedTest bmsServiceClient;

	@Before
	public void setup() {
		bmsServiceClient = new BmsRestClientServiceUnifiedTest();
		MockitoAnnotations.initMocks(this);
	}

	/**
	 * Important Points How to insert or update Property on BO Panel Note - First verify
	 * all URL and secret key present in BmsRestClientServiceUnifiedTest as constant.
	 * step1 : Decide how u want to populate your payload by file or Manually +++ Also
	 * decide env u want to set property for. step2 : First get Property using key, which
	 * you want to update or insert. step3 : Depends on the requirement add or modify the
	 * property using Action - INSERT or UPDATE. step4 : Again get the property from BO
	 * Panel to verify.
	 */

	// @Test
	public void getPropertyFromBoPanelTest() {
		try {
			String key = "testingKey";
			String env = "stage";

			// First get Property using key
			String existingValue = bmsServiceClient.getProperty(key, env);
			log.info("Current Value for key : {}, env : {}, is :  {} ", key, env, existingValue);

			// either read by file or directly assign value
			BmsServicePropertiesUpdateDto updateDto = null;
			updateDto = (BmsServicePropertiesUpdateDto) getPayloadManually(); // or
																				// getPayloadByFile()

			// Call updateProperty function to add or update property in BO Panel
			bmsServiceClient.updateProperty(updateDto, env);

			// get Property after inserting or udpate
			String updatedValue = bmsServiceClient.getProperty(key, env);
			log.info("Updated Value for key : {}, env : {}, is :  {} ", key, env, updatedValue);

		}
		catch (Exception ex) {
			Assert.fail();
		}
	}

	public Object getPayloadByFile() {

		try {
			return TestUtils.getObjectFromFile("BoPanelPayLoad", BmsServicePropertiesUpdateDto.class);
		}
		catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public Object getPayloadManually() {
		try {
			BmsServicePropertiesUpdateDto updateDto = new BmsServicePropertiesUpdateDto();
			BmsPropertiesDto property = new BmsPropertiesDto();
			property.setName("dummyKey");
			property.setValue("dummyValue");
			updateDto.setAction("INSERT");
			updateDto.setProperties(Arrays.asList(property));
			return updateDto;
		}
		catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

}
