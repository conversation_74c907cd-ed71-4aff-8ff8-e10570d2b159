package com.org.com.webapiv2.utility.boPanel;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.google.common.hash.Hashing;
import com.org.com.webapiv2.dto.BmsPropertiesGetDto;
import com.org.com.webapiv2.dto.BmsServicePropertiesUpdateDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static com.org.com.webapiv2.apisExecution.utility.E2eTestsUtility.objectMapper;
import static com.org.com.webapiv2.constants.MonitoringConstants.CLIENT;

@Component
@Log4j2
public class BmsRestClientServiceUnifiedTest {

	private static final String SERVICE_CLIENT_NAME = "BMSService";

	private static final String PAYLOAD_HASH = "payloadHash";

	private final String keyForStage;

	private final String keyForProd;

	private final String domainForStage;

	private final String domainForProd;

	@Autowired
	public BmsRestClientServiceUnifiedTest() {
		/*
		 * check property file for value key.for.boPanel.stage key.for.boPanel.prod
		 */

		this.keyForStage = "don't know now";
		this.keyForProd = "don't know now";

		this.domainForStage = "https://phs-staging-internal.paytm.com";
		this.domainForProd = "https://phs-internal.paytm.com";
	}

	public String getHeaders(final String jsonDto, final String env) {
		try {
			String jwtHash = Hashing.sha256().hashString(jsonDto, StandardCharsets.UTF_8).toString();

			Map<String, String> claims = new HashMap<>();
			claims.put(PAYLOAD_HASH, jwtHash);
			claims.put(CLIENT, "pth");
			return createJwtTokenForBoPanel(env.equalsIgnoreCase("stage") ? keyForStage : keyForProd, claims);
		}
		catch (Exception ex) {
			log.error("Exception while creating headers Client: {} for BoPanel, Exception: {}", SERVICE_CLIENT_NAME,
					CommonsUtility.exceptionFormatter(ex));
			throw ex;
		}
	}

	public static String createJwtTokenForBoPanel(final String secret, final Map<String, String> claims) {
		try {
			if (StringUtils.isBlank(secret)) {
				return null;
			}
			JWTCreator.Builder jwtBuilder = JWT.create();

			claims.forEach((key, value) -> {
				if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
					jwtBuilder.withClaim(key, value);
				}
			});
			return jwtBuilder.sign(Algorithm.HMAC256(secret));
		}
		catch (Exception e) {
			log.error("Exception while creating JWT Token claims: {}, Exception: {}", claims,
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	public void updateProperty(final BmsServicePropertiesUpdateDto updateDto, final String env) {

		// Make a call to update the property
		try {
			String url = (env.equalsIgnoreCase("stage") ? this.domainForStage : this.domainForProd)
					+ "/phs/int/pth/update/properties";
			String jsonPayload = objectMapper.writeValueAsString(updateDto);

			// Create Token
			String jwtToken = getHeaders(jsonPayload, env);

			// Create an HTTP client
			CloseableHttpClient httpClient = HttpClients.createDefault();

			// Create an HTTP POST request
			HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Authorization", jwtToken);
			httpPost.setHeader("Client", "pth");
			httpPost.setHeader("Content-Type", "application/json");

			// Set the JSON payload as the entity of the request
			HttpEntity entity = new StringEntity(jsonPayload);
			httpPost.setEntity(entity);

			// Send the request
			CloseableHttpResponse response = httpClient.execute(httpPost);

			// Handle the response as needed
			log.info("Response status for update Property Request: " + response);

			// Close resources
			response.close();
			httpClient.close();
		}
		catch (IOException ex) {
			throw new RuntimeException(ex);
		}
	}

	public String getProperty(final String key, final String env) {

		// Make a call to update the property
		try {

			String url = (env.equalsIgnoreCase("stage") ? this.domainForStage : this.domainForProd)
					+ "/phs/int/pth/get/properties";

			BmsPropertiesGetDto getPropertyDto = new BmsPropertiesGetDto(1, 300);
			String jsonPayload = objectMapper.writeValueAsString(getPropertyDto);

			// Create Token
			String jwtToken = getHeaders(jsonPayload, env);

			// Create an HTTP client
			CloseableHttpClient httpClient = HttpClients.createDefault();

			// Create an HTTP POST request
			HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Authorization", jwtToken);
			httpPost.setHeader("Client", "pth");
			httpPost.setHeader("Content-Type", "application/json");

			// Set the JSON payload as the entity of the request
			HttpEntity entity = new StringEntity(jsonPayload);
			httpPost.setEntity(entity);

			// Send the request
			CloseableHttpResponse response = httpClient.execute(httpPost);

			// Handle the response
			int statusCode = response.getStatusLine().getStatusCode();
			log.info("Response status code for get property request: " + statusCode);

			// Read and parse the response body
			BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
			StringBuilder responseBody = new StringBuilder();
			String line;

			while ((line = reader.readLine()) != null) {
				responseBody.append(line);
			}

			// Close resources
			response.close();
			httpClient.close();

			// Parse JSON response and extract data
			JSONObject jsonResponse = new JSONObject(responseBody.toString());
			boolean success = jsonResponse.getBoolean("success");
			String message = jsonResponse.getString("message");

			if (success) {
				JSONArray data = jsonResponse.getJSONArray("data");
				for (int i = 0; i < data.length(); i++) {
					JSONObject property = data.getJSONObject(i);
					String name = property.getString("name");
					if (name.equals(key)) {
						return property.getString("value");
					}
				}
				return null;
			}
			else {
				log.info("Error message while getting property value for key {} is {} ", key, message);
				return null;
			}
		}
		catch (IOException | JSONException ex) {
			throw new RuntimeException(ex);
		}
	}

}
